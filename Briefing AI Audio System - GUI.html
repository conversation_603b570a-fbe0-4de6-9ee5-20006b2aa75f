<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Voice Production System GUI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* CSS Variables for Theming */
        :root { /* Dark Mode Defaults */
            --color-bg-primary: #0F0F0F; /* Overall very dark background */
            --color-bg-secondary: #1A1A1A; /* Cards, main content background */
            --color-bg-tertiary: #2D2D2D; /* Inputs, tab button background */
            --color-bg-light: #3A3A3A; /* Hover states, secondary buttons */
            --color-bg-list-item: #1F1F1F; /* Specific list item background */
            --color-bg-welcome-gradient-from: #1A1A1A;
            --color-bg-welcome-gradient-to: #2D2D2D;

            --color-text-primary: #E0E0E0; /* Main text */
            --color-text-secondary: #A0AEC0; /* Secondary text, labels */
            --color-text-accent: #63B3ED; /* Headings, primary icons */
            --color-text-link: #4299E1; /* Links */

            --color-border-primary: #3A3A3A; /* General element borders */
            --color-border-secondary: #4A4A4A; /* Input borders */

            --color-accent-blue: #63B3ED; /* Primary interactive blue */
            --color-accent-blue-hover: #4299E1;
            --color-accent-blue-dark: #2C5282; /* Used for darker accents like project info box */

            --shadow-sm: rgba(0, 0, 0, 0.1);
            --shadow-md: rgba(0, 0, 0, 0.2);
            --shadow-lg: rgba(0, 0, 0, 0.3);
            --shadow-xl: rgba(0, 0, 0, 0.4);

            --chart-bar-color-1: rgb(59, 130, 246); /* Blue-500 for completed */
            --chart-bar-color-2: rgb(96, 165, 250); /* Blue-400 for in-progress */
            --chart-bar-color-3: rgb(156, 163, 175); /* Gray-400 for future */
            --chart-border-color-1: rgb(37, 99, 235);
            --chart-border-color-2: rgb(59, 130, 246);
            --chart-border-color-3: rgb(107, 114, 128);
            --chart-axis-text: #A0AEC0;
            --chart-grid-line: #4A4A4A;
        }

        body.light-mode {
            --color-bg-primary: #F0F0F0;
            --color-bg-secondary: #FFFFFF;
            --color-bg-tertiary: #EAEAEA;
            --color-bg-light: #DCDCDC;
            --color-bg-list-item: #FDFDFD;
            --color-bg-welcome-gradient-from: #FDFDFD;
            --color-bg-welcome-gradient-to: #EAEAEA;


            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-accent: #007AFF; /* Apple Blue */
            --color-text-link: #0056B3;

            --color-border-primary: #E0E0E0;
            --color-border-secondary: #D0D0D0;

            --color-accent-blue: #007AFF;
            --color-accent-blue-hover: #0056B3;
            --color-accent-blue-dark: #EAF2F8; /* Light blue accent box for light mode */

            --shadow-sm: rgba(0, 0, 0, 0.03);
            --shadow-md: rgba(0, 0, 0, 0.05);
            --shadow-lg: rgba(0, 0, 0, 0.1);
            --shadow-xl: rgba(0, 0, 0, 0.15);

            --chart-bar-color-1: rgb(0, 122, 255);
            --chart-bar-color-2: rgb(102, 178, 255);
            --chart-bar-color-3: rgb(190, 190, 190);
            --chart-border-color-1: rgb(0, 100, 200);
            --chart-border-color-2: rgb(80, 150, 220);
            --chart-border-color-3: rgb(150, 150, 150);
            --chart-axis-text: #666666;
            --chart-grid-line: #D0D0D0;
        }

        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: var(--color-bg-primary);
            color: var(--color-text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh; /* Ensure body takes full viewport height */
        }
        html {
            height: 100%; /* Ensure html also takes full height for proper layout */
        }

        /* Custom scrollbar */
        body::-webkit-scrollbar { width: 8px; }
        body::-webkit-scrollbar-track { background: var(--color-bg-tertiary); border-radius: 10px; }
        body::-webkit-scrollbar-thumb { background: var(--color-bg-light); border-radius: 10px; }
        body::-webkit-scrollbar-thumb:hover { background: var(--color-text-secondary); }

        /* Fixed Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px; /* h-16 */
            background-color: rgba(var(--color-bg-primary-rgb), 0.8); /* Use RGBA for backdrop-blur, calculated in JS */
            backdrop-filter: blur(10px);
            z-index: 50;
            box-shadow: 0 1px 2px var(--shadow-sm);
            border-bottom: 1px solid var(--color-border-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem; /* px-4 */
        }
        header .font-semibold { color: var(--color-text-primary); }
        header .text-gray-400 { color: var(--color-text-secondary); }
        header button:hover { background-color: var(--color-bg-tertiary); }
        #projectDropdownContent {
            background-color: var(--color-bg-secondary);
            box-shadow: 0 8px 16px var(--shadow-lg);
            border: 1px solid var(--color-border-primary);
        }
        #projectDropdownContent a { color: var(--color-text-primary); }
        #projectDropdownContent a:hover { background-color: var(--color-bg-tertiary); }

        /* Fixed Secondary Navigation */
        .sticky-nav {
            position: fixed;
            top: 64px; /* Below header */
            left: 0;
            right: 0;
            height: 56px; /* h-14 equivalent, adjust as needed */
            background-color: rgba(var(--color-bg-secondary-rgb), 0.9); /* Use RGBA for backdrop-blur, calculated in JS */
            backdrop-filter: blur(8px);
            z-index: 40;
            box-shadow: 0 1px 2px var(--shadow-sm);
            border-bottom: 1px solid var(--color-border-primary);
            display: flex;
            flex-wrap: wrap;
            justify-content: center; /* Center the buttons */
            align-items: center;
            gap: 0.5rem; /* gap-2 */
            padding: 0.75rem 1rem; /* p-3 and horizontal padding */
            width: 100%; /* Ensure it spans full width */
            box-sizing: border-box; /* Include padding in width */
        }

        /* Main Content Area: Left Sidebar + Central Workspace */
        .main-layout-container {
            display: flex;
            flex-grow: 1; /* Takes remaining vertical space */
            margin-top: 120px; /* Offset for header (64px) + nav (56px) */
            overflow: hidden; /* Hide overflow to prevent scrollbars from this container */
        }

        /* Fixed Left Sidebar */
        aside {
            position: fixed;
            top: 120px; /* Below header and secondary nav */
            bottom: 0; /* Extends to bottom of viewport */
            left: 0;
            width: 250px; /* max-w-[250px] fixed width */
            background-color: var(--color-bg-secondary);
            border-right: 1px solid var(--color-border-primary);
            padding: 1rem; /* p-4 */
            display: flex;
            flex-direction: column;
            gap: 1rem; /* space-y-4 */
            overflow-y: auto; /* Scrollable sidebar content */
            z-index: 30; /* Below nav, above main content in z-index */
        }
        @media (max-width: 767px) { /* Hide sidebar on small screens */
            aside { display: none; }
        }

        /* Main Content Area (Scrollable) */
        main {
            flex-grow: 1; /* Takes remaining horizontal space */
            margin-left: 250px; /* Offset for fixed sidebar */
            background-color: var(--color-bg-primary); /* Use primary background for main content */
            padding: 1rem 2rem; /* p-4 lg:p-8 */
            overflow-y: auto; /* Main content is scrollable */
        }
        @media (max-width: 767px) { /* Adjust main content on small screens */
            main { margin-left: 0; }
        }

        .section-container {
            max-width: 900px; /* max-w-4xl is too wide for fixed sidebar layout often, adjust as needed */
            margin-left: auto;
            margin-right: auto;
            display: flex;
            flex-direction: column;
            gap: 3rem; /* space-y-12 */
        }

        /* Styling for section headers */
        .section-header {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-text-accent);
            margin-bottom: 2rem;
            padding-top: 2rem; /* Adjusted padding-top for smooth scroll target */
            margin-top: -2rem; /* Counteract padding-top for smooth scroll target */
        }

        /* Chart container styling */
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 400px;
            margin-top: 2rem;
            margin-bottom: 2rem;
            background-color: var(--color-bg-secondary); /* Chart background */
            padding: 1rem;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px var(--shadow-md);
            border: 1px solid var(--color-border-primary);
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }

        /* Custom tab buttons */
        .tab-button {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            color: var(--color-text-secondary);
            background-color: var(--color-bg-tertiary);
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border: none;
        }
        .tab-button:hover {
            background-color: var(--color-bg-light);
            color: var(--color-text-primary);
        }
        .tab-button.active {
            background-color: var(--color-accent-blue);
            color: white;
            box-shadow: 0 4px 6px var(--shadow-md);
        }
        .tab-content {
            display: none;
            padding: 1.5rem;
            background-color: var(--color-bg-secondary);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px var(--shadow-md), 0 2px 4px -1px var(--shadow-sm);
            margin-top: 1rem;
            border: 1px solid var(--color-border-primary);
        }
        .tab-content.active { display: block; }

        /* SSML tag styling */
        .ssml-tag {
            background-color: var(--color-accent-blue-dark);
            color: var(--color-text-primary);
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.875rem;
            white-space: nowrap;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-flex;
            align-items: center;
            cursor: help;
        }
        .ssml-tag:hover { background-color: var(--color-accent-blue-hover); }
        .ssml-tag-tooltip {
            visibility: hidden;
            width: 200px;
            background-color: #555; /* Fixed dark for tooltip, always readable */
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .ssml-tag:hover .ssml-tag-tooltip { visibility: visible; opacity: 1; }
        .ssml-tag-tooltip::after {
            content: "";
            position: absolute;
            top: 100%; left: 50%; margin-left: -5px;
            border-width: 5px; border-style: solid;
            border-color: #555 transparent transparent transparent;
        }

        /* Dashboard tiles */
        .dashboard-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }
        @media (min-width: 640px) { .dashboard-grid { grid-template-columns: repeat(2, minmax(0, 1fr)); } }
        @media (min-width: 1024px) { .dashboard-grid { grid-template-columns: repeat(3, minmax(0, 1fr)); } }
        .dashboard-tile {
            background-color: var(--color-bg-secondary);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px var(--shadow-md), 0 2px 4px -1px var(--shadow-sm);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border: 1px solid var(--color-border-primary);
        }
        .dashboard-tile:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px var(--shadow-lg), 0 4px 6px -2px var(--shadow-md);
            border-color: var(--color-accent-blue);
        }
        .dashboard-tile-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--color-text-accent);
        }
        .dashboard-tile-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        /* Generic input styling */
        .input-field {
            width: 100%;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid var(--color-border-secondary);
            background-color: var(--color-bg-tertiary);
            color: var(--color-text-primary);
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .input-field:focus {
            outline: none;
            border-color: var(--color-accent-blue);
            box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.5); /* Use fixed rgba for blur consistency */
        }

        /* Range slider styling */
        .slider-container { width: 100%; margin-bottom: 1rem; }
        .slider-label { font-weight: 600; color: var(--color-text-secondary); margin-bottom: 0.5rem; display: block; }
        .slider {
            -webkit-appearance: none; appearance: none;
            width: 100%; height: 10px;
            background: var(--color-bg-tertiary);
            outline: none; opacity: 0.7;
            -webkit-transition: .2s;
            transition: opacity .2s;
            border-radius: 5px;
        }
        .slider:hover { opacity: 1; }
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none; appearance: none;
            width: 20px; height: 20px;
            background: var(--color-accent-blue);
            cursor: pointer; border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .slider::-moz-range-thumb {
            width: 20px; height: 20px;
            background: var(--color-accent-blue);
            cursor: pointer; border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Button styling */
        .btn-primary {
            background-color: var(--color-accent-blue);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
            cursor: pointer;
            border: none;
        }
        .btn-primary:hover {
            background-color: var(--color-accent-blue-hover);
            transform: translateY(-1px);
        }
        .btn-secondary {
            background-color: var(--color-bg-light);
            color: var(--color-text-primary);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
            cursor: pointer;
            border: none;
        }
        .btn-secondary:hover {
            background-color: var(--color-bg-tertiary);
            transform: translateY(-1px);
        }

        /* Drag & Drop zone */
        .drag-drop-zone {
            border: 2px dashed var(--color-border-secondary);
            border-radius: 0.75rem;
            padding: 2rem;
            text-align: center;
            color: var(--color-text-secondary);
            background-color: var(--color-bg-list-item);
            transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
        }
        .drag-drop-zone.drag-over {
            background-color: var(--color-accent-blue-dark);
            border-color: var(--color-accent-blue);
            color: var(--color-text-primary);
        }

        /* Basic modal styling */
        .modal-overlay {
            display: none; position: fixed; z-index: 1000;
            left: 0; top: 0; width: 100%; height: 100%;
            overflow: auto; background-color: rgba(0, 0, 0, 0.6);
            align-items: center; justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }
        .modal-content {
            background-color: var(--color-bg-secondary);
            color: var(--color-text-primary);
            padding: 2rem; border-radius: 0.75rem;
            box-shadow: 0 8px 16px var(--shadow-xl);
            width: 90%; max-width: 500px;
            animation: slideIn 0.3s ease-out; text-align: center;
        }
        .modal-header { font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; color: var(--color-text-primary); }
        .modal-body { margin-bottom: 1.5rem; color: var(--color-text-secondary); }
        .modal-footer button { margin: 0 0.5rem; }

        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideIn { from { transform: translateY(-50px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }

        /* Dropdown specific styles for navigation */
        .nav-dropdown-btn {
            background-color: var(--color-bg-tertiary); /* Solid background for nav dropdown button */
            color: var(--color-text-secondary);
            padding: 0.75rem 1.5rem; border-radius: 0.5rem;
            font-weight: 600; transition: all 0.2s ease-in-out;
            cursor: pointer; border: none;
            display: flex; align-items: center; gap: 0.5rem;
        }
        .nav-dropdown-btn:hover { background-color: var(--color-bg-light); color: var(--color-text-primary); }
        .nav-dropdown-content {
            display: none; position: absolute;
            background-color: var(--color-bg-secondary);
            min-width: 220px;
            box-shadow: 0 8px 16px 0px var(--shadow-lg);
            z-index: 100; border-radius: 0.5rem; overflow: hidden;
            top: 100%; left: 0; margin-top: 0.5rem; padding: 0.5rem 0;
            border: 1px solid var(--color-border-primary);
        }
        .nav-dropdown:hover .nav-dropdown-content { display: block; }
        .nav-dropdown-content a {
            color: var(--color-text-primary);
            padding: 0.75rem 1.5rem; text-decoration: none;
            display: block; text-align: left;
        }
        .nav-dropdown-content a:hover { background-color: var(--color-bg-tertiary); }

        /* Styling for chapter overview list */
        .chapter-list a {
            display: block; padding: 0.75rem 1.5rem;
            border-radius: 0.5rem; margin-bottom: 0.5rem;
            background-color: var(--color-bg-list-item);
            color: var(--color-text-secondary); text-decoration: none;
            transition: all 0.2s ease-in-out;
            border: 1px solid var(--color-border-primary);
        }
        .chapter-list a:hover {
            background-color: var(--color-bg-tertiary);
            color: var(--color-text-primary);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-sm);
        }

        /* Specific section styling (using variables) */
        section {
            background-color: var(--color-bg-secondary);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px var(--shadow-md);
            padding: 1.5rem 2rem; /* p-6 lg:p-8 */
            border: 1px solid var(--color-border-primary);
        }
        /* Welcome section specific */
        #welcome {
            background-image: linear-gradient(to bottom right, var(--color-bg-welcome-gradient-from), var(--color-bg-welcome-gradient-to));
            border-color: var(--color-border-primary);
        }
        #welcome h1 { color: var(--color-text-accent); }
        #welcome h2 { color: var(--color-text-accent); }
        #welcome p { color: var(--color-text-secondary); }

        /* Project dashboard specific */
        #dashboard .project-info-box { /* Custom class for the project info box */
            background-color: var(--color-accent-blue-dark);
            color: var(--color-text-primary);
            border: 1px solid var(--color-border-primary);
        }
        #dashboard .bg-gray-850 { /* This class is no longer used, replaced by list-item styling directly */
            background-color: var(--color-bg-list-item);
            border: 1px solid var(--color-border-primary);
        }

        /* Text colors for specific elements that were explicitly defined with Tailwind */
        .text-gray-100 { color: var(--color-text-primary); }
        .text-gray-200 { color: var(--color-text-primary); }
        .text-gray-300 { color: var(--color-text-primary); }
        .text-gray-400 { color: var(--color-text-secondary); }
        .text-gray-500 { color: var(--color-text-secondary); }
        .text-blue-400 { color: var(--color-text-accent); }
        .text-blue-500 { color: var(--color-text-link); } /* Specific for ElevenLabs link */
        .text-green-400 { color: #34C759; } /* Apple green */
        .text-yellow-400 { color: #FFCC00; } /* Apple yellow */
        .text-orange-400 { color: #FF9500; } /* Apple orange */

        /* Specific overrides for the workspace mockup diagram */
        .border.border-gray-700 { border-color: var(--color-border-primary); }
        .bg-blue-900 { background-color: var(--color-accent-blue-dark); } /* This class is no longer used, replaced by style attribute */
        .bg-blue-950 { background-color: rgba(var(--color-accent-blue-rgb), 0.2); } /* A light blue for contrast in diagram */
        .bg-gray-800 { background-color: var(--color-bg-tertiary); } /* This class is no longer used, replaced by style attribute */
        .text-blue-200 { color: var(--color-text-accent); } /* This class is no longer used, replaced by style attribute */
        .bg-blue-800 { background-color: var(--color-accent-blue-hover); } /* This class is no longer used, replaced by style attribute */
    </style>
</head>
<body>
    <!-- Overall container that manages the flex layout -->
    <div class="h-screen flex flex-col">

        <!-- A. Top Bar (Global Header) - Fixed -->
        <header>
            <!-- Left Side: Project Dropdown -->
            <div class="relative">
                <button id="projectDropdownBtn" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">🗂️</span> <!-- Folder/Briefcase Icon -->
                    <span class="font-semibold text-gray-100">Project</span>
                    <span class="text-gray-400">▼</span>
                </button>
                <div id="projectDropdownContent" class="hidden absolute top-full left-0 mt-2 w-48 rounded-lg shadow-lg py-2 z-60" style="background-color: var(--color-bg-secondary); border: 1px solid var(--color-border-primary);">
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Create New Project</a>
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Open Project</a>
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Switch Project</a>
                </div>
            </div>

            <!-- Right Side: Global Actions -->
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">💡</span> <!-- Lightbulb/Moon Icon -->
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">⚙️</span> <!-- Gear Icon -->
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">❓</span> <!-- Question Mark Icon -->
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">👤</span> <!-- Person Icon (Optional) -->
                </button>
            </div>
        </header>

        <!-- Fixed Secondary Navigation (directly below header) -->
        <nav class="sticky-nav">
            <a href="#welcome" class="tab-button">Welcome</a>
            <div class="relative nav-dropdown">
                <button class="nav-dropdown-btn">
                    Chapter Overview ▼
                </button>
                <div class="nav-dropdown-content">
                    <a href="#welcome" onclick="event.preventDefault(); document.getElementById('welcome').scrollIntoView({ behavior: 'smooth' });">Welcome</a>
                    <a href="#chapter-overview" onclick="event.preventDefault(); document.getElementById('chapter-overview').scrollIntoView({ behavior: 'smooth' });">Chapter Overview</a>
                    <a href="#context" onclick="event.preventDefault(); document.getElementById('context').scrollIntoView({ behavior: 'smooth' });">I. Project Context & Objectives</a>
                    <a href="#dashboard" onclick="event.preventDefault(); document.getElementById('dashboard').scrollIntoView({ behavior: 'smooth' });">II. Project Dashboard</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('tts-v-cloning-tab', 'core-features')">III. Interactive Core Functionalities</a>
                    <a href="#workspace" onclick="event.preventDefault(); document.getElementById('workspace').scrollIntoView({ behavior: 'smooth' });">IV. Workspace Layout & Views</a>
                    <a href="#phases" onclick="event.preventDefault(); document.getElementById('phases').scrollIntoView({ behavior: 'smooth' });">V. Development Phases Plan</a>
                    <a href="#settings" onclick="event.preventDefault(); document.getElementById('settings').scrollIntoView({ behavior: 'smooth' });">VI. Preferences and Customization</a>
                    <a href="#enhancers" onclick="event.preventDefault(); document.getElementById('enhancers').scrollIntoView({ behavior: 'smooth' });">VII. Workflow Enhancers</a>
                    <a href="#full-briefing" onclick="event.preventDefault(); document.getElementById('full-briefing').scrollIntoView({ behavior: 'smooth' });">VIII. Full GUI Briefing Document</a>
                </div>
            </div>
            <a href="#context" class="tab-button">Context</a>
            <a href="#dashboard" class="tab-button">Dashboard</a>
            <!-- Core Features Dropdown -->
            <div class="relative nav-dropdown">
                <button class="nav-dropdown-btn">
                    Core Features ▼
                </button>
                <div class="nav-dropdown-content">
                    <a href="#core-features" onclick="setActiveTabWithScroll('tts-v-cloning-tab')">Text-to-Speech & Voice Cloning</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('sts-module-tab')">Speech-to-Speech</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('sfx-module-tab')">Text-to-SFX</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('music-module-tab')">Text-to-Music</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('voice-finder-tab')">Voice Finder</a>
                    <a href="#core-features" onclick="setActiveTabWithScroll('batch-processing-tab')">Batch Processing</a>
                </div>
            </div>
            <a href="#workspace" class="tab-button">Workspace</a>
            <a href="#phases" class="tab-button">Phases</a>
            <a href="#settings" class="tab-button">Settings</a>
            <a href="#enhancers" class="tab-button">Enhancers</a>
            <a href="#full-briefing" class="tab-button">Full Briefing</a>
        </nav>

        <!-- Main Content Area: Left Sidebar + Central Workspace (takes remaining height) -->
        <div class="main-layout-container">
            <!-- B. Left Sidebar (Navigation & Project Explorer) - Fixed -->
            <aside>
                <!-- Project Explorer -->
                <div class="p-3 rounded-lg shadow-md" style="background-color: var(--color-bg-light); border: 1px solid var(--color-border-primary);">
                    <h3 class="font-bold text-md mb-2 flex items-center space-x-2" style="color: var(--color-text-primary);">
                        <span class="text-lg">📂</span> Project Explorer
                    </h3>
                    <ul class="text-sm space-y-1" style="color: var(--color-text-secondary);">
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open folder &quot;My_Awesome_Project_V1&quot;')">├── My_Awesome_Project_V1</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open folder &quot;input/&quot;')">│   ├── 📁 input/</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open file &quot;script_part1.txt&quot;')">│   │   ├── script_part1.txt</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open file &quot;reference_audio.wav&quot;')">│   │   └── reference_audio.wav</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open folder &quot;output/&quot;')">│   ├── 📁 output/</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open file &quot;intro_dialogue.wav&quot;')">│   │   ├── intro_dialogue.wav</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open file &quot;main_narration.wav&quot;')">│   │   └── main_narration.wav</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open folder &quot;meta/&quot;')">│   ├── 📁 meta/</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open folder &quot;versions/&quot;')">│   └── 📁 versions/</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Project Explorer', 'Simulated Action: Open project &quot;Old_Project_Archive&quot;')">└── Old_Project_Archive</li>
                    </ul>
                </div>

                <!-- Recently Generated Audios -->
                <div class="p-3 rounded-lg shadow-md flex-1 overflow-y-auto" style="background-color: var(--color-bg-light); border: 1px solid var(--color-border-primary);">
                    <h3 class="font-bold text-md mb-2 flex items-center space-x-2" style="color: var(--color-text-primary);">
                        <span class="text-lg">🎵</span> Recently Generated Audios
                    </h3>
                    <ul class="text-sm space-y-2" style="color: var(--color-text-secondary);">
                        <li class="flex justify-between items-center p-2 rounded-md cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border: 1px solid var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for Narration_Chap1.wav:\\nVoice: Sarah (Warm)\\nScript Excerpt: &quot;...this is the beginning of the story.&quot;\\nGeneration Date: June 05, 2025\\n\\nParameter Reference: Emotional Intensity 70%, Speaking Rate 1.1x')">
                            <span>Narration_Chap1.wav</span>
                            <div class="flex space-x-1">
                                <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing Narration_Chap1.wav...')">▶️</button>
                                <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving Narration_Chap1.wav locally.')">💾</button>
                            </div>
                        </li>
                        <li class="flex justify-between items-center p-2 rounded-md cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border: 1px solid var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for SFX_DoorCreak.wav:\\nDescription: Soft creaking of a wooden door.\\nGeneration Date: June 04, 2025\\n\\nParameter Reference: Duration 3s, Intensity 60%')">
                            <span>SFX_DoorCreak.wav</span>
                            <div class="flex space-x-1">
                                <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing SFX_DoorCreak.wav...')">▶️</button>
                                <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving SFX_DoorCreak.wav locally.')">💾</button>
                            </div>
                        </li>
                        <li class="flex justify-between items-center p-2 rounded-md cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border: 1px solid var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for Music_CafeJazz.mp3:\\nGenre: Jazz\\nMood: Relaxed, Background\\nGeneration Date: June 03, 2025\\n\\nParameter Reference: Tempo 120 BPM, Duration 60s')">
                            <span>Music_CafeJazz.mp3</span>
                            <div class="flex space-x-1">
                                <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing Music_CafeJazz.mp3...')">▶️</button>
                                <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving Music_CafeJazz.mp3 locally.')">💾</button>
                            </div>
                        </li>
                        <li class="flex justify-between items-center p-2 rounded-md cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border: 1px solid var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for Dialogue_Emma_V2.wav:\\nVoice: Emma (V2)\\nScript Excerpt: &quot;...the revision is complete!&quot;\\nGeneration Date: June 02, 2025\\n\\nParameter Reference: Emotional Intensity 80%, Pitch -1')">
                            <span>Dialogue_Emma_V2.wav</span>
                            <div class="flex space-x-1">
                                <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing Dialogue_Emma_V2.wav...')">▶️</button>
                                <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving Dialogue_Emma_V2.wav locally.')">💾</button>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Voice Library (Compact View) -->
                <div class="p-3 rounded-lg shadow-md" style="background-color: var(--color-bg-light); border: 1px solid var(--color-border-primary);">
                    <h3 class="font-bold text-md mb-2 flex items-center space-x-2" style="color: var(--color-text-primary);">
                        <span class="text-lg">🎙️</span> Voice Library
                    </h3>
                    <ul class="text-sm space-y-1" style="color: var(--color-text-secondary);">
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Voice Info', 'Details for Standard Male Voice 1:\\nType: Standard\\nCharacteristics: Clear, concise')">• Standard Male Voice 1</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Voice Info', 'Details for Cloned Voice: Sarah (Warm):\\nType: Cloned\\nCharacteristics: Warm, soothing, friendly')">• Cloned Voice: Sarah (Warm)</li>
                        <li class="cursor-pointer hover:text-blue-400" onclick="showCustomModal('Voice Info', 'Details for Cloned Voice: John (Authoritative):\\nType: Cloned\\nCharacteristics: Authoritative, deep, resonant')">• Cloned Voice: John (Authoritative)</li>
                    </ul>
                    <button class="w-full mt-3 btn-secondary flex items-center justify-center space-x-2" onclick="showCustomModal('Voice Lab Wizard', 'Starts the wizard for cloning/editing voices. This would involve multiple steps, e.g., audio upload, analysis, fine-tuning of voice characteristics.')">
                        <span class="text-lg">➕👤🎙️</span> Generate/Edit New Voice
                    </button>
                </div>
            </aside>

            <!-- C. Central Workspace (Dynamic Main Content Area) - Scrollable -->
            <main>
                <div class="section-container">

                    <!-- Section: Welcome / Introduction -->
                    <section id="welcome">
                        <h1 class="text-5xl font-extrabold mb-6 leading-tight">
                            AI Voice Production System GUI
                        </h1>
                        <p class="text-xl mb-8 max-w-2xl mx-auto">
                            This interactive application provides insight into the **architectural mindmap** of our AI voice production system. It outlines the **development phases** and the comprehensive range of functions planned for this implementation.
                        </p>
                        <h2 class="text-3xl font-bold mb-6">Quick Access to Core Modules:</h2>
                        <div class="dashboard-grid max-w-3xl mx-auto">
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('tts-v-cloning-tab', 'core-features')">
                                <span class="dashboard-tile-icon">💬</span>
                                <span class="dashboard-tile-text">Generate New Dialogue</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('batch-processing-tab', 'core-features')">
                                <span class="dashboard-tile-icon">📜</span>
                                <span class="dashboard-tile-text">Import Script for Batch Processing</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('sts-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🎙️➡️🎙️</span>
                                <span class="dashboard-tile-text">Start Speech-to-Speech</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('sfx-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🔊</span>
                                <span class="dashboard-tile-text">Generate Sound Effect</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('music-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🎶</span>
                                <span class="dashboard-tile-text">Generate Music Track</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('voice-finder-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🔍🎙️</span>
                                <span class="dashboard-tile-text">Open Voice Finder</span>
                            </div>
                        </div>
                        <p class="text-lg mt-8" style="color: var(--color-text-secondary);">
                            Scroll down or use the navigation above to explore all details!
                        </p>
                    </section>

                    <!-- Section: Chapter Overview -->
                    <section id="chapter-overview">
                        <h2 class="section-header">Chapter Overview</h2>
                        <p class="mb-6" style="color: var(--color-text-secondary);">
                            This overview serves as an interactive table of contents to quickly navigate to the most important sections and modules of this documentation.
                        </p>
                        <div class="chapter-list grid grid-cols-1 md:grid-cols-2 gap-4">
                            <a href="#welcome" onclick="event.preventDefault(); document.getElementById('welcome').scrollIntoView({ behavior: 'smooth' });">Welcome</a>
                            <a href="#chapter-overview" onclick="event.preventDefault(); document.getElementById('chapter-overview').scrollIntoView({ behavior: 'smooth' });">Chapter Overview</a>
                            <a href="#context" onclick="event.preventDefault(); document.getElementById('context').scrollIntoView({ behavior: 'smooth' });">I. Project Context & Objectives</a>
                            <a href="#dashboard" onclick="event.preventDefault(); document.getElementById('dashboard').scrollIntoView({ behavior: 'smooth' });">II. Project Dashboard</a>
                            <a href="#core-features" onclick="setActiveTabWithScroll('tts-v-cloning-tab', 'core-features')">III. Interactive Core Functionalities</a>
                            <a href="#workspace" onclick="event.preventDefault(); document.getElementById('workspace').scrollIntoView({ behavior: 'smooth' });">IV. Workspace Layout & Views</a>
                            <a href="#phases" onclick="event.preventDefault(); document.getElementById('phases').scrollIntoView({ behavior: 'smooth' });">V. Development Phases Plan</a>
                            <a href="#settings" onclick="event.preventDefault(); document.getElementById('settings').scrollIntoView({ behavior: 'smooth' });">VI. Preferences and Customization</a>
                            <a href="#enhancers" onclick="event.preventDefault(); document.getElementById('enhancers').scrollIntoView({ behavior: 'smooth' });">VII. Workflow Enhancers</a>
                            <a href="#full-briefing" onclick="event.preventDefault(); document.getElementById('full-briefing').scrollIntoView({ behavior: 'smooth' });">VIII. Full GUI Briefing Document</a>
                        </div>
                    </section>


                    <!-- Section: Project Context & Objectives -->
                    <section id="context">
                        <h2 class="section-header">I. Project Context & Objectives</h2>
                        <p class="mb-4" style="color: var(--color-text-primary);">
                            This application serves as an interactive briefing for the Graphical User Interface (GUI) of a professional, locally operated AI audio system. The system is designed for use in studio environments and is aimed at non-technical users for creating commercials, voiceovers, and dialogues. The primary goal of the GUI is to enable a fast, creative, and intuitive workflow, where the complexity of the AI models remains in the background.
                        </p>
                        <h3 class="text-2xl font-semibold mb-3" style="color: var(--color-text-accent);">Overall Vision & Core Design Principles</h3>
                        <p class="mb-4" style="color: var(--color-text-primary);">
                            <strong>Vision:</strong> To create an outstanding, intuitive, and efficient GUI that serves as the absolute core for rapid, creative, and professional audio production in a studio environment. It must empower non-technical users by hiding underlying AI complexity, requiring minimal clicks, providing immediate feedback, and ensuring a shallow learning curve. Inspiration is drawn from the clarity and user guidance of <a href="https://elevenlabs.io" target="_blank" class="text-blue-500 hover:underline" style="color: var(--color-text-link);">ElevenLabs.io</a>.
                        </p>
                        <h4 class="text-xl font-medium mb-2" style="color: var(--color-text-link);">Aesthetics & Usability:</h4>
                        <ul class="list-disc list-inside space-y-1" style="color: var(--color-text-primary);">
                            <li><strong>Style:</strong> Modern, minimalist, professional, clean, and uncluttered.</li>
                            <li><strong>Color Scheme:</strong> A calm, harmonious palette, grounded in warm neutrals (e.g., stone, cream) for main backgrounds and components. Complementary colors (e.g., teal, subtle greens) for secondary areas and sparingly used as accents for calls to action or highlights. Total number of colors minimal.</li>
                            <li><strong>Typography:</strong> Clean, highly legible sans-serif fonts (e.g., 'Inter' or similar). Clear hierarchy for headings, labels, and body text.</li>
                        </ul>
                        <h4 class="text-xl font-medium mt-4 mb-2" style="color: var(--color-text-link);">Interactivity Principles:</h4>
                        <ul class="list-disc list-inside space-y-1" style="color: var(--color-text-primary);">
                            <li><strong>Minimal Clicks:</strong> Frequently used functions are directly accessible.</li>
                            <li><strong>Instant Feedback:</strong> All interactions (button clicks, slider movements, file uploads, generation processes) must provide immediate visual feedback (e.g., smooth loading bars, status changes, waveform updates).</li>
                            <li><strong>Consistency:</strong> Uniform interaction patterns and UI components across all views.</li>
                            <li><strong>Error Prevention:</strong> Clear labels, tooltips, immediate input validations, and hiding irrelevant options.</li>
                        </ul>
                        <p class="mt-4" style="color: var(--color-text-primary);">
                            <strong>Responsiveness:</strong> The layout must adapt seamlessly to all screen sizes (laptops to large studio displays) and remain free from lag. No horizontal scrolling.
                        </p>
                    </section>

                    <!-- Section: Project Dashboard -->
                    <section id="dashboard">
                        <h2 class="section-header">II. Project Dashboard</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            The dashboard is the central starting point after opening a project and offers direct access to all core generation and management modules. Click on one of the tiles to start the respective function.
                        </p>
                        <div class="project-info-box p-4 rounded-lg font-semibold mb-6">
                            Project: My_Awesome_Project_V1 | Date: June 05, 2025 | Generated Audios: 42 | Used Voices: 8
                        </div>

                        <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Quick Access Modules</h3>
                        <div class="dashboard-grid">
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('tts-v-cloning-tab', 'core-features')">
                                <span class="dashboard-tile-icon">💬</span>
                                <span class="dashboard-tile-text">Generate New Dialogue</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('batch-processing-tab', 'core-features')">
                                <span class="dashboard-tile-icon">📜</span>
                                <span class="dashboard-tile-text">Import Script for Batch Processing</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('sts-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🎙️➡️🎙️</span>
                                <span class="dashboard-tile-text">Start Speech-to-Speech</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('sfx-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🔊</span>
                                <span class="dashboard-tile-text">Generate Sound Effect</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('music-module-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🎶</span>
                                <span class="dashboard-tile-text">Generate Music Track</span>
                            </div>
                            <div class="dashboard-tile" style="background-color: var(--color-bg-secondary);" onclick="setActiveTabWithScroll('voice-finder-tab', 'core-features')">
                                <span class="dashboard-tile-icon">🔍🎙️</span>
                                <span class="dashboard-tile-text">Open Voice Finder</span>
                            </div>
                        </div>

                        <h3 class="text-2xl font-semibold mt-8 mb-4" style="color: var(--color-text-accent);">Recently Generated Audios</h3>
                        <ul class="space-y-2" style="color: var(--color-text-primary);">
                            <li class="flex justify-between items-center p-2 rounded-md border cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border-color: var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for Narration_Chap1.wav:\\nVoice: Sarah (Warm)\\nScript Excerpt: &quot;...this is the beginning of the story.&quot;\\nGeneration Date: June 05, 2025\\n\\nParameter Reference: Emotional Intensity 70%, Speaking Rate 1.1x')">
                                <span>Narration_Chap1.wav</span>
                                <div class="flex space-x-1">
                                    <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing Narration_Chap1.wav...')">▶️</button>
                                    <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving Narration_Chap1.wav locally.')">💾</button>
                                </div>
                            </li>
                            <li class="flex justify-between items-center p-2 rounded-md border cursor-pointer hover:bg-gray-700" style="background-color: var(--color-bg-list-item); border-color: var(--color-border-primary);" onclick="showCustomModal('Audio Details', 'Details for SFX_DoorCreak.wav:\\nDescription: Soft creaking of a wooden door.\\nGeneration Date: June 04, 2025\\n\\nParameter Reference: Duration 3s, Intensity 60%')">
                                <span>SFX_DoorCreak.wav</span>
                                <div class="flex space-x-1">
                                    <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Play', 'Playing SFX_DoorCreak.wav...')">▶️</button>
                                    <button class="text-green-400 hover:text-green-300 text-lg" onclick="event.stopPropagation(); showCustomModal('Save', 'Saving SFX_DoorCreak.wav locally.')">💾</button>
                                </div>
                            </li>
                        </ul>
                    </section>

                    <!-- Section: Core Functionalities (Tabs for Modules) -->
                    <section id="core-features">
                        <h2 class="section-header">III. Interactive Core Functionalities</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            This section presents the detailed views of the core modules of the application. Each module is implemented as an interactive tab to facilitate navigation and understanding.
                        </p>

                        <div class="flex flex-wrap gap-2 mb-6 border-b pb-4" style="border-color: var(--color-border-primary);">
                            <button class="tab-button active" onclick="setActiveTab('tts-v-cloning-tab')">Text-to-Speech & Voice Cloning</button>
                            <button class="tab-button" onclick="setActiveTab('sts-module-tab')">Speech-to-Speech</button>
                            <button class="tab-button" onclick="setActiveTab('sfx-module-tab')">Text-to-SFX</button>
                            <button class="tab-button" onclick="setActiveTab('music-module-tab')">Text-to-Music</button>
                            <button class="tab-button" onclick="setActiveTab('voice-finder-tab')">Voice Finder</button>
                            <button class="tab-button" onclick="setActiveTab('batch-processing-tab')">Batch Processing</button>
                        </div>

                        <!-- Tab Content: Text-to-Speech (TTS) & Voice Cloning (V-Cloning) View -->
                        <div id="tts-v-cloning-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Text-to-Speech (TTS) & Voice Cloning</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                The core area for speech generation and voice cloning, optimized for fast iterations and precise control.
                            </p>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="md:col-span-2">
                                    <label for="scriptEditor" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Narrative Structuring & Editor</label>
                                    <textarea id="scriptEditor" rows="10" class="input-field mb-4" placeholder="Enter your script here. SSML-like tags such as &lt;break time='500ms'/&gt; or &lt;prosody pitch='+5st'&gt;Text with increased pitch&lt;/prosody&gt; are supported and visually highlighted."></textarea>

                                    <div class="mb-4 flex flex-wrap gap-2">
                                        <span class="ssml-tag">
                                            &lt;break&gt;
                                            <span class="ssml-tag-tooltip">Inserts a pause (e.g., time="500ms")</span>
                                        </span>
                                        <span class="ssml-tag">
                                            &lt;prosody&gt;
                                            <span class="ssml-tag-tooltip">Controls pitch, rate, volume</span>
                                        </span>
                                        <span class="ssml-tag">
                                            &lt;emphasis&gt;
                                            <span class="ssml-tag-tooltip">Emphasizes text (level="strong")</span>
                                        </span>
                                        <span class="ssml-tag">
                                            &lt;lang&gt;
                                            <span class="ssml-tag-tooltip">Defines the language of a text segment</span>
                                        </span>
                                        <span class="ssml-tag">
                                            &lt;voice&gt;
                                            <span class="ssml-tag-tooltip">Changes the voice within the script</span>
                                        </span>
                                    </div>

                                    <label class="block font-semibold mb-2" style="color: var(--color-text-primary);">Drag & Drop Zone (Text Import)</label>
                                    <div id="textDropZone" class="drag-drop-zone h-32 flex items-center justify-center">
                                        Drag text files (.txt, .docx, .csv) here or click to upload
                                    </div>
                                </div>
                                <div>
                                    <label for="voiceSelect" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Voice Selection</label>
                                    <select id="voiceSelect" class="input-field mb-4">
                                        <option>Standard Male Voice 1</option>
                                        <option>Cloned Voice: Sarah (Warm)</option>
                                        <option>Cloned Voice: John (Authoritative)</option>
                                        <option>Pre-trained Voice: Anna (Calm)</option>
                                    </select>
                                    <button class="w-full btn-secondary mb-4">Clone New Voice (Wizard)</button>

                                    <label class="block font-semibold mb-2" style="color: var(--color-text-primary);">Reference Audio Upload (for Cloning)</label>
                                    <div id="audioDropZone" class="drag-drop-zone h-32 flex items-center justify-center">
                                        Drag audio snippets (.wav, .mp3) here
                                    </div>
                                </div>
                            </div>

                            <h3 class="text-2xl font-semibold mt-8 mb-4" style="color: var(--color-text-accent);">Parameter Control & Emotion Presets</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div class="space-y-4">
                                    <div class="slider-container">
                                        <label for="emotionIntensity" class="slider-label">Emotional Intensity: <span id="emotionValue">50%</span></label>
                                        <input type="range" id="emotionIntensity" min="0" max="100" value="50" class="slider">
                                    </div>
                                    <div class="slider-container">
                                        <label for="speakingRate" class="slider-label">Speaking Rate: <span id="rateValue">1.0x</span></label>
                                        <input type="range" id="speakingRate" min="0.5" max="2.0" step="0.1" value="1.0" class="slider">
                                    </div>
                                    <div class="slider-container">
                                        <label for="pitchProsody" class="slider-label">Pitch / Prosody: <span id="pitchValue">0</span></label>
                                        <input type="range" id="pitchProsody" min="-5" max="5" value="0" class="slider">
                                    </div>
                                    <div class="slider-container">
                                        <label for="emphasis" class="slider-label">Emphasis: <span id="emphasisValue">50%</span></label>
                                        <input type="range" id="emphasis" min="0" max="100" value="50" class="slider">
                                    </div>
                                    <div class="slider-container">
                                        <label for="volume" class="slider-label">Volume: <span id="volumeValue">100%</span></label>
                                        <input type="range" id="volume" min="0" max="100" value="100" class="slider">
                                    </div>
                                </div>
                                <div>
                                    <h4 class="font-semibold mb-2" style="color: var(--color-text-primary);">Emotion Presets:</h4>
                                    <div class="grid grid-cols-2 gap-2">
                                        <button class="btn-secondary text-sm" onclick="applyPreset('happy')">Happy 😊</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('sad')">Sad 😔</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('angry')">Angry 😡</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('neutral')">Neutral 😐</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('excited')">Excited 🤩</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('whispering')">Whispering 🤫</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('commercial')">Commercial 📈</button>
                                        <button class="btn-secondary text-sm" onclick="applyPreset('professional')">Professional 👔</button>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center mt-8">
                                <button class="btn-secondary">Preview (Partial)</button>
                                <button class="btn-primary">Generate Audio</button>
                                <button class="btn-primary">Export Audio</button>
                            </div>
                            <div class="w-full h-24 rounded-lg mt-6 flex items-center justify-center border" style="background-color: var(--color-bg-light); color: var(--color-text-secondary); border-color: var(--color-border-primary);">
                                Output Waveform / Playback (Placeholder)
                            </div>
                            <div class="flex space-x-2 mt-4 justify-center">
                                <button class="btn-secondary text-sm">Normalize</button>
                                <button class="btn-secondary text-sm">Trim</button>
                                <button class="btn-secondary text-sm">Basic Compression</button>
                            </div>
                        </div>

                        <!-- Tab Content: Speech-to-Speech (STS) Module View -->
                        <div id="sts-module-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Speech-to-Speech (STS) Module</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                Convert existing speech recordings into a new voice or style.
                            </p>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block font-semibold mb-2" style="color: var(--color-text-primary);">Source Audio Input</label>
                                    <div id="stsAudioDropZone" class="drag-drop-zone h-32 flex items-center justify-center mb-4">
                                        Drag .wav or .mp3 files here
                                    </div>
                                    <div class="w-full h-16 rounded-lg mt-2 flex items-center justify-center mb-4 border" style="background-color: var(--color-bg-light); color: var(--color-text-secondary); border-color: var(--color-border-primary);">
                                        Source Audio Player (Placeholder)
                                    </div>
                                    <label for="transcriptionField" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Transcription (Editable)</label>
                                    <textarea id="transcriptionField" rows="5" class="input-field" placeholder="Transcription of the source audio, editable for corrections..."></textarea>
                                </div>
                                <div>
                                    <label for="stsTargetVoiceSelect" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Target Voice Selection</label>
                                    <select id="stsTargetVoiceSelect" class="input-field mb-4">
                                        <option>Standard Female Voice 1</option>
                                        <option>Cloned Voice: Emily (Friendly)</option>
                                    </select>
                                    <div class="slider-container">
                                        <label for="stsEmotionIntensity" class="slider-label">Emotional Intensity: <span id="stsEmotionValue">50%</span></label>
                                        <input type="range" id="stsEmotionIntensity" min="0" max="100" value="50" class="slider">
                                    </div>
                                    <div class="slider-container">
                                        <label for="stsPitchProsody" class="slider-label">Pitch / Prosody: <span id="stsPitchValue">0</span></label>
                                        <input type="range" id="stsPitchProsody" min="-5" max="5" value="0" class="slider">
                                    </div>
                                    <button class="w-full btn-primary mt-4">Generate STS Audio</button>
                                    <div class="w-full h-24 rounded-lg mt-6 flex items-center justify-center border" style="background-color: var(--color-bg-light); color: var(--color-text-secondary); border-color: var(--color-border-primary);">
                                        Output Waveform / Playback (Placeholder)
                                    </div>
                                    <button class="w-full btn-primary mt-4">Export STS Audio</button>
                                </div>
                            </div>
                        </div>

                        <!-- Tab Content: Text-to-SFX Module View -->
                        <div id="sfx-module-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Text-to-SFX Module</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                Generate sound effects from text descriptions.
                            </p>
                            <label for="sfxDescription" class="block font-semibold mb-2" style="color: var(--color-text-primary);">SFX Description</label>
                            <textarea id="sfxDescription" rows="5" class="input-field mb-4" placeholder="e.g., 'door creaking', 'light rain', 'distant thunder'"></textarea>

                            <label for="sfxType" class="block font-semibold mb-2" style="color: var(--color-text-primary);">SFX Type/Style</label>
                            <select id="sfxType" class="input-field mb-4">
                                <option>Nature</option>
                                <option>Industrial</option>
                                <option>Human Sounds</option>
                                <option>Cinematic</option>
                                <option>Foley</option>
                            </select>

                            <div class="slider-container">
                                <label for="sfxDuration" class="slider-label">Duration (seconds): <span id="sfxDurationValue">5</span></label>
                                <input type="range" id="sfxDuration" min="1" max="30" value="5" class="slider">
                            </div>
                            <div class="slider-container">
                                <label for="sfxIntensity" class="slider-label">Intensity: <span id="sfxIntensityValue">50%</span></label>
                                <input type="range" id="sfxIntensity" min="0" max="100" value="50" class="slider">
                            </div>
                            <div class="slider-container">
                                <label for="sfxPitch" class="slider-label">Pitch Shift: <span id="sfxPitchValue">0</span></label>
                                <input type="range" id="sfxPitch" min="-10" max="10" value="0" class="slider">
                            </div>

                            <button class="w-full btn-primary mt-6">Generate SFX</button>
                            <div class="w-full h-24 rounded-lg mt-6 flex items-center justify-center border" style="background-color: var(--color-bg-light); color: var(--color-text-secondary); border-color: var(--color-border-primary);">
                                Output Waveform / Playback (Placeholder)
                            </div>
                            <button class="w-full btn-primary mt-4">Export SFX</button>
                        </div>

                        <!-- Tab Content: Text-to-Music Module View -->
                        <div id="music-module-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Text-to-Music Module</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                Generate music tracks from text descriptions and musical parameters.
                            </p>
                            <label for="musicDescription" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Music Description</label>
                            <textarea id="musicDescription" rows="5" class="input-field mb-4" placeholder="e.g., 'upbeat jazz track for a cafe scene', 'melancholic piano melody'"></textarea>

                            <label for="musicGenre" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Genre/Mood</label>
                            <select id="musicGenre" class="input-field mb-4">
                                <option>Jazz</option>
                                <option>Classical</option>
                                <option>Electronic</option>
                                <option>Ambient</option>
                                <option>Rock</option>
                            </select>

                            <div class="slider-container">
                                <label for="musicTempo" class="slider-label">Tempo (BPM): <span id="musicTempoValue">120</span></label>
                                <input type="range" id="musicTempo" min="40" max="200" value="120" class="slider">
                            </div>
                            <div class="slider-container">
                                <label for="musicDuration" class="slider-label">Duration (seconds): <span id="musicDurationValue">60</span></label>
                                <input type="range" id="musicDuration" min="10" max="300" value="60" class="slider">
                            </div>
                            <div class="slider-container">
                                <label for="musicIntensity" class="slider-label">Intensity/Dynamics: <span id="musicIntensityValue">50%</span></label>
                                <input type="range" id="musicIntensity" min="0" max="100" value="50" class="slider">
                            </div>

                            <button class="w-full btn-primary mt-6">Generate Music</button>
                            <div class="w-full h-24 rounded-lg mt-6 flex items-center justify-center border" style="background-color: var(--color-bg-light); color: var(--color-text-secondary); border-color: var(--color-border-primary);">
                                Output Waveform / Playback (Placeholder)
                            </div>
                            <button class="w-full btn-primary mt-4">Export Music</button>
                        </div>

                        <!-- Tab Content: Voice Finder View -->
                        <div id="voice-finder-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Voice Finder</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                Find suitable voices from the library based on text descriptions of desired characteristics or a sample text.
                            </p>
                            <label for="voiceDescriptionInput" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Describe the desired voice or paste sample text:</label>
                            <textarea id="voiceDescriptionInput" rows="7" class="input-field mb-4" placeholder="e.g., 'a warm, deep male voice for documentary narration, authoritative yet calm.' Or paste a sample text here."></textarea>

                            <button class="w-full btn-primary mb-6" onclick="findVoice()">Find Voice</button>

                            <h4 class="text-xl font-medium mb-3" style="color: var(--color-text-link);">Matching Voices:</h4>
                            <div id="voiceResults" class="space-y-3">
                                <!-- Voice search results will be injected here by JavaScript -->
                                <div class="p-3 rounded-md flex items-center justify-between shadow-sm border" style="background-color: var(--color-bg-list-item); border-color: var(--color-border-primary);">
                                    <div>
                                        <span class="font-semibold" style="color: var(--color-text-primary);">Cloned Voice: Sarah (Warm)</span>
                                        <p class="text-sm" style="color: var(--color-text-secondary);">Ideal for soothing content.</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <span class="font-bold text-lg text-green-400">92%</span>
                                        <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="showCustomModal('Playback', 'Playing sample for &quot;Cloned Voice: Sarah (Warm)&quot;...')">▶️</button>
                                        <button class="btn-secondary text-sm py-1 px-3" onclick="showCustomModal('Selected', 'Voice &quot;Cloned Voice: Sarah (Warm)&quot; selected!')">Select</button>
                                    </div>
                                </div>
                                <div class="p-3 rounded-md flex items-center justify-between shadow-sm border" style="background-color: var(--color-bg-list-item); border-color: var(--color-border-primary);">
                                    <div>
                                        <span class="font-semibold" style="color: var(--color-text-primary);">Standard Male Voice 1</span>
                                        <p class="text-sm" style="color: var(--color-text-secondary);">Clear and concise.</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <span class="font-bold text-lg text-yellow-400">78%</span>
                                        <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="showCustomModal('Playback', 'Playing sample for &quot;Standard Male Voice 1&quot;...')">▶️</button>
                                        <button class="btn-secondary text-sm py-1 px-3" onclick="showCustomModal('Selected', 'Voice &quot;Standard Male Voice 1&quot; selected!')">Select</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab Content: Batch Processing View -->
                        <div id="batch-processing-tab" class="tab-content">
                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Batch Processing</h3>
                            <p class="mb-4" style="color: var(--color-text-primary);">
                                Efficient generation of multiple dialogue lines or entire scripts for speech, SFX, and music.
                            </p>
                            <label for="batchScriptEditor" class="block font-semibold mb-2" style="color: var(--color-text-primary);">Script Editor (Line by Line)</label>
                            <textarea id="batchScriptEditor" rows="12" class="input-field mb-4" placeholder="Line 1: [Voice: Sarah] This is the first line of dialogue.
Line 2: [SFX: Door Creak] A door creaks.
Line 3: [Music: Cafe Jazz] Background music begins.
Line 4: [Voice: John] And here comes the next speaker line."></textarea>

                            <div class="flex space-x-2 mb-6">
                                <button class="btn-secondary">Import Script</button>
                                <button class="btn-secondary">Export Script</button>
                            </div>

                            <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Batch Control & Output</h3>
                            <button class="w-full btn-primary mb-6">Generate All</button>

                            <h4 class="font-semibold mb-2" style="color: var(--color-text-primary);">Progress & Task Queue:</h4>
                            <div class="p-4 rounded-lg space-y-3 border" style="background-color: var(--color-bg-list-item); border-color: var(--color-border-primary);">
                                <div class="flex items-center space-x-2">
                                    <span style="color: var(--color-text-primary);">1. Narration_Chap1 (Voice: Sarah)</span>
                                    <div class="flex-1 h-2 rounded-full overflow-hidden" style="background-color: var(--color-bg-light);"><div class="h-full rounded-full" style="background-color: var(--color-accent-blue); width: 75%;"></div></div>
                                    <span class="text-sm" style="color: var(--color-text-secondary);">75% (ETA: 10s)</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span style="color: var(--color-text-primary);">2. SFX_DoorCreak</span>
                                    <div class="flex-1 h-2 rounded-full overflow-hidden" style="background-color: var(--color-bg-light);"><div class="h-full rounded-full" style="background-color: var(--color-accent-blue); width: 0%;"></div></div>
                                    <span class="text-sm" style="color: var(--color-text-secondary);">Waiting</span>
                                </div>
                            </div>

                            <h4 class="font-semibold mt-6 mb-2" style="color: var(--color-text-primary);">Output Management:</h4>
                            <p style="color: var(--color-text-primary);">Generated Filenames: `[Date]_[Job Number]_[LineIndex]_[Type].wav`</p>
                            <button class="w-full btn-primary mt-4">Open Output Folder</button>
                        </div>
                    </section>

                    <!-- Section: Workspace Layout -->
                    <section id="workspace">
                        <h2 class="section-header">IV. Workspace Layout & Views</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            The GUI is designed as a modern, professional desktop application that adapts fluidly to different screen sizes. It is clearly divided into three persistent areas to maximize efficiency.
                        </p>
                        <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Visual Conceptualization</h3>
                        <div class="border rounded-xl overflow-hidden shadow-lg mb-8" style="border-color: var(--color-border-primary);">
                            <div class="h-16 text-white flex items-center justify-between px-4 font-bold" style="background-color: var(--color-accent-blue-dark);">
                                <span>Project Name - GUI Title</span>
                                <span>⚙️ ❓ 👤</span>
                            </div>
                            <div class="flex h-96">
                                <div class="w-1/4 p-4 flex flex-col justify-between" style="background-color: var(--color-bg-tertiary); color: var(--color-text-primary);">
                                    <div class="text-sm font-semibold">
                                        📁 Project Explorer<br>
                                        &nbsp;├── My_Project<br>
                                        &nbsp;└── 📁 output/<br>
                                    </div>
                                    <div class="text-sm font-semibold">
                                        🎵 Recently Generated<br>
                                        &nbsp;&nbsp;&nbsp;• Audio 1<br>
                                        &nbsp;&nbsp;&nbsp;• Audio 2<br>
                                    </div>
                                    <div class="text-sm font-semibold">
                                        🎙️ Voice Library<br>
                                        <div class="mt-2 text-center text-xs p-1 rounded-md" style="background-color: var(--color-accent-blue); color: white;">➕ Generate/Edit New Voice</div>
                                    </div>
                                </div>
                                <div class="flex-1 p-4 flex items-center justify-center text-center font-bold text-2xl" style="background-color: var(--color-bg-secondary); color: var(--color-text-accent);">
                                    Central Workspace<br>
                                    (Dynamic Content Area: TTS, STS, etc.)
                                </div>
                            </div>
                        </div>
                        <p style="color: var(--color-text-primary);">
                            This visual representation clarifies the division into the <strong>top bar</strong> (Global Header), the <strong>left sidebar</strong> (Navigation &amp; Project Explorer), and the <strong>central workspace</strong> (Dynamic Main Content Area).
                        </p>
                    </section>

                    <!-- Section: Development Phases Plan -->
                    <section id="phases">
                        <h2 class="section-header">V. Development Phases Plan</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            The development of the application proceeds in phases, with each phase delivering specific main functions and improvements. Progress is visualized by a dynamic bar chart.
                        </p>
                        <div class="chart-container">
                            <canvas id="phasesChart"></canvas>
                        </div>
                        <h3 class="text-2xl font-semibold mt-8 mb-4" style="color: var(--color-text-accent);">Deliverables Summary:</h3>
                        <div class="space-y-4" style="color: var(--color-text-primary);">
                            <div>
                                <h4 class="font-bold text-lg mb-1">Phase 1: Core GUI &amp; Speech Generation</h4>
                                <ul class="list-disc list-inside ml-4">
                                    <li>Fully functional GUI (TTS, V-Cloning, STS, Dashboard, Batch Processing for Speech).</li>
                                    <li>SSML Tag Toolbar.</li>
                                    <li>Voice control sliders.</li>
                                    <li>Reference voice upload.</li>
                                    <li>Audio export (WAV, MP3).</li>
                                    <li>Basic project management and settings.</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-1">Phase 2: Expanding Generative Capabilities &amp; Workflow Enhancements</h4>
                                <ul class="list-disc list-inside ml-4">
                                    <li>Integration of Text-to-SFX and Text-to-Music Modules.</li>
                                    <li>Integration of the <strong>Voice Finder</strong> module.</li>
                                    <li>Real-time Preview (for all generation types).</li>
                                    <li>Session Recovery / Autosave, Voice Tagging &amp; Metadata, Undo / Redo Stack.</li>
                                    <li>Background Processing, keyboard shortcuts, Smart Error Handling.</li>
                                    <li>Initial integration with AVID Pro Tools API (if technically feasible).</li>
                                    <li>Extended project management (history, Smart Versioning, search/filter), automatic PDF documentation.</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-1">Phase 3: Advanced Features, Multi-User &amp; Full Integration</h4>
                                <ul class="list-disc list-inside ml-4">
                                    <li>Multi-User / Login functionality.</li>
                                    <li>Full integration points with <strong>AVID Pro Tools API</strong> (and potentially other editors).</li>
                                    <li>Further advanced features based on user feedback.</li>
                                    <li>Comprehensive quality control functions.</li>
                                </ul>
                            </div>
                        </div>
                    </section>

                    <!-- Section: Preferences & Customization -->
                    <section id="settings">
                        <h2 class="section-header">VI. Preferences and Customization</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            In this area, users can adapt the system to their individual or project-specific requirements to improve efficiency, consistency, personalization, and professionalism.
                        </p>
                        <h3 class="text-2xl font-semibold mb-4" style="color: var(--color-text-accent);">Settings Categories</h3>
                        <div class="space-y-6">
                            <div>
                                <h4 class="font-bold text-lg mb-2">General/UI:</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <label class="block" style="color: var(--color-text-primary);">Theme:
                                        <select class="input-field"><option>Dark</option><option>Light</option></select>
                                    </label>
                                    <label class="block" style="color: var(--color-text-primary);">GUI Language:
                                        <select class="input-field"><option>English</option><option>German</option></select>
                                    </label>
                                    <label class="block" style="color: var(--color-text-primary);">Font Size/Scaling:
                                        <input type="range" min="80" max="120" value="100" class="slider">
                                    </label>
                                    <label class="block" style="color: var(--color-text-primary);">Default Save Location:
                                        <input type="text" class="input-field" value="/Users/<USER>/AudioProjects">
                                    </label>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Project Management:</h4>
                                <label class="block" style="color: var(--color-text-primary);">Default Folder Structure: <button class="btn-secondary ml-2 text-sm">Configure</button></label>
                                <label class="block mt-2" style="color: var(--color-text-primary);">Auto-Save Interval: <input type="number" class="input-field w-24 inline-block" value="5"> minutes</label>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Audio Generation (Speech):</h4>
                                <label class="block" style="color: var(--color-text-primary);">Default Voice:
                                    <select class="input-field"><option>Standard Male Voice 1</option><option>Cloned Voice: Sarah</option></select>
                                </label>
                                <label class="block mt-2" style="color: var(--color-text-primary);">Default Emotion Preset:
                                    <select class="input-field"><option>Neutral</option><option>Commercial</option></select>
                                </label>
                                <label class="block mt-2" style="color: var(--color-text-primary);">Quality Settings:
                                    <select class="input-field"><option>44.1 kHz</option><option>48 kHz</option></select>
                                    <select class="input-field ml-2"><option>16-bit</option><option>24-bit</option></select>
                                </label>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Dynamic File Naming Templates:</h4>
                                <label class="block" style="color: var(--color-text-primary);">Template Editor:
                                    <input type="text" class="input-field mt-1" value="[Date]_[ProjectName]_[Character]_[Index]">
                                </label>
                                <p class="text-sm mt-1" style="color: var(--color-text-secondary);">Live Preview: 2025-06-05_MyProject_Emma_001.wav</p>
                                <button class="btn-secondary mt-2 text-sm">Save Template</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section: Workflow Enhancers -->
                    <section id="enhancers">
                        <h2 class="section-header">VII. Workflow Enhancers</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            These functions enhance user-friendliness and efficiency, prioritized after core features:
                        </p>
                        <ul class="list-disc list-inside space-y-2" style="color: var(--color-text-primary);">
                            <li><strong>Real-time Preview:</strong> Fast, partial synthesis preview before final generation.</li>
                            <li><strong>Session Recovery / Autosave:</strong> Resume state after crashes or power loss.</li>
                            <li><strong>Voice Tagging &amp; Metadata:</strong> Add tags to voice models ("warm", "deep", "fast").</li>
                            <li><strong>Undo / Redo Stack:</strong> Allows undoing or redoing recent settings or parameter changes.</li>
                            <li><strong>Background Processing:</strong> Task queue with ETA and progress per item, notifications upon completion.</li>
                            <li><strong>Keyboard Shortcuts:</strong> For power users: Trigger actions via keys (e.g., CTRL+S, CTRL+ENTER).</li>
                            <li><strong>Multi-User / Login (Phase 3+):</strong> Admin, editor, and viewer roles, personalized GUI settings per user.</li>
                            <li><strong>Integration Points (Future):</strong> "Send to Editor" button for direct API integration with AVID Pro Tools, Adobe Premiere Pro, and DaVinci Resolve.</li>
                            <li><strong>Smart Error Handling:</strong> Human-readable messages + suggestions, link to troubleshooting tips/logs.</li>
                        </ul>
                    </section>

                    <!-- Section: Full GUI Briefing Document -->
                    <section id="full-briefing">
                        <h2 class="section-header">VIII. Full GUI Briefing Document</h2>
                        <p class="mb-6" style="color: var(--color-text-primary);">
                            This section contains the complete text of the original GUI briefing document as a comprehensive reference. It is embedded here for full documentation.
                        </p>
                        <div class="prose max-w-none" style="color: var(--color-text-primary);">
                            <h1 class="text-3xl font-bold mb-4" style="color: var(--color-text-primary);">AI Voice Production System GUI: Comprehensive Development Mindmap &amp; Interactive Prompt</h1>
                            <p class="text-lg mb-6" style="color: var(--color-text-primary);">This prompt serves as a complete development blueprint for a sophisticated Artificial Intelligence Voice Production System Graphical User Interface (GUI). It is designed for an expert AI frontend developer, data analyst, UI/UX designer, and information architect to generate a single-page interactive web application (SPA). The SPA's primary goal is to make the extensive "Structured GUI Briefing &ndash; AI Voice Production System" content easily consumable, explorable, and interactive for non-technical users, particularly studio professionals.</p>

                            <p class="text-lg mb-6" style="color: var(--color-text-primary);">The application structure will not mirror the report's original layout; instead, it will feature a <strong>thematic, task-oriented structure</strong> with clear navigation and dynamic content presentation. This approach is chosen to prioritize <strong>user understanding and ease of navigation</strong>, allowing users to either follow a guided path or jump directly to relevant sections.</p>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">I. Overall Vision &amp; Core Design Principles</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);"><strong>Vision:</strong> To create an outstanding, intuitive, and efficient GUI that serves as the absolute core for rapid, creative, and professional audio production in a studio environment. It must empower non-technical users by hiding underlying AI complexity, requiring minimal clicks, providing immediate feedback, and ensuring a shallow learning curve. Inspiration is drawn from the clarity and user guidance of <a href="https://elevenlabs.io" target="_blank" class="text-blue-500 hover:underline" style="color: var(--color-text-link);">ElevenLabs.io</a>.</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Aesthetics &amp; Usability:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Style:</strong> Modern, minimalist, professional, clean, and uncluttered.</li>
                                <li><strong>Color Scheme:</strong> A calm, harmonious palette, grounded in warm neutrals (e.g., stone, cream) for main backgrounds and components. Complementary colors (e.g., teal, subtle greens) for secondary areas and sparingly used as accents for calls to action or highlights. Total number of colors minimal.</li>
                                <li><strong>Typography:</strong> Clean, highly legible sans-serif fonts (e.g., 'Inter' or similar). Clear hierarchy for headings, labels, and body text.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Interactivity Principles:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Minimal Clicks:</strong> Frequently used functions are directly accessible.</li>
                                <li><strong>Instant Feedback:</strong> All interactions (button clicks, slider movements, file uploads, generation processes) must provide immediate visual feedback (e.g., smooth loading bars, status changes, waveform updates).</li>
                                <li><strong>Consistency:</strong> Uniform interaction patterns and UI components across all views.</li>
                                <li><strong>Error Prevention:</strong> Clear labels, tooltips, immediate input validations, and hiding irrelevant options.</li>
                            </ul>
                            <p class="mb-6" style="color: var(--color-text-primary);"><strong>Responsiveness:</strong> The layout must adapt seamlessly to all screen sizes (laptops to large studio displays) and remain free from lag. No horizontal scrolling.</p>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">II. Information Architecture &amp; SPA Structure (Roadmap/Mindmap)</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);">The application is structured as a <strong>single-page, scrollable website with a sticky top navigation bar</strong>. Each major chapter or thematic area of the GUI briefing document is translated into a distinct scrollable HTML &lt;section&gt;.</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Rationale for this structure:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Linear Progression:</strong> Users can scroll through the entire briefing linearly, mirroring a traditional document read.</li>
                                <li><strong>Non-Linear Navigation:</strong> The sticky top navigation allows instant jumps to any major section, providing a quick-reference "mindmap" of the application's content.</li>
                                <li><strong>Detail on Demand:</strong> Interactive elements (tabs, charts, styled mockups) within each section reveal deeper levels of detail without cluttering the initial view.</li>
                                <li><strong>User Understanding:</strong> This approach simplifies the consumption of a complex technical briefing for a non-technical audience by segmenting information logically and visually.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Main Sections &amp; Navigation Targets:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li>`#welcome`: Introduction to the GUI</li>
                                <li>`#chapter-overview`: Interactive Table of Contents</li>
                                <li>`#context`: Project Context &amp; Objectives</li>
                                <li>`#dashboard`: Project Dashboard (Quick Access to Modules)</li>
                                <li>`#core-features`: Interactive Core Functionalities (with tabs for each module)</li>
                                <li>`#workspace`: Visual Workspace Layout</li>
                                <li>`#phases`: Development Phases Plan (Deliverables)</li>
                                <li>`#settings`: Preferences &amp; Customization</li>
                                <li>`#enhancers`: Workflow Enhancers (Nice-to-Haves)</li>
                                <li>`#full-briefing`: Full GUI Briefing Document (Comprehensive Reference)</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">III. Global Layout (Main Window Components)</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);">The GUI's main window will feature a persistent, three-part layout:</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">A. Top Bar (Global Header):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Global actions and navigation.</li>
                                <li><strong>Implementation:</strong> Implemented with `position: fixed; top: 0; z-index: 50;`.</li>
                                <li><strong>Elements:</strong> "Project" Dropdown/Button (Text, Icon: Folder), Global Settings (Gear Icon), Help/Support (Question Mark Icon), and Theme Toggle (Lightbulb/Moon Icon).</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">B. Left Sidebar (Navigation &amp; Project Explorer):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Description:</strong> A permanently visible and scrollable sidebar for project navigation and library access.</li>
                                <li><strong>Implementation:</strong> Implemented with `position: fixed; top: 120px; bottom: 0; left: 0; width: 250px; overflow-y: auto;`. This ensures it stays fixed alongside the main content and is independently scrollable.</li>
                                <li><strong>Elements:</strong> "Project Explorer" (Tree View/List mock with clickable folders/files), "Recently Generated Audios" Panel (scrollable list with simulated detail dialogs on click), "Voice Library (Compact View)" (quick access to voices, expands to simulated "Voice Lab Wizard").</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">C. Central Workspace (Dynamic Main Content Area):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li><strong>Description:</strong> The largest and most dynamic area of the GUI, adapting its content based on the selected work mode (e.g., TTS, STS, Project View).</li>
                                <li><strong>Functionality:</strong> Houses the core functional modules, interactive elements, and detailed information displays.</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">IV. Detailed Views / Modules (Interactive Core Features)</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);">This section details the design of the dynamic central workspace views. Each module will be represented by an interactive tab within the `#core-features` section.</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">A. Project Dashboard (Default View when opening a project)</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Instant overview and quick actions for the active project.</li>
                                <li><strong>Layout:</strong> Top (Project Name, Date, User, summary statistics), Middle (Tiles/Buttons for: "Generate New Dialogue" 💬, "Import Script for Batch Processing" 📜, "Start Speech-to-Speech" 🎙️➡️🎙️, "Generate Sound Effect" 🔊, "Generate Music Track" 🎶, "Open Voice Lab" 🔬), Bottom (Recently Generated Audios list, quick access to project notes).</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">B. Text-to-Speech (TTS) &amp; Voice Cloning (V-Cloning) View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Core area for speech generation and voice cloning, optimized for fast iterations and precise control.</li>
                                <li><strong>Layout:</strong> Upper Area (Text-Input &amp; Voice Selection), Lower Area (Parameter Control &amp; Output).</li>
                                <li><strong>Upper Area (Input):</strong> Narrative Structuring &amp; Editor (rich-text editor, chapter creation, voice assignment, SSML-like tags), Drag &amp; Drop Zone (text files), Project Initialization from Diverse Formats.</li>
                                <li><strong>Right Sidebar Area (Voice Selection &amp; Reference):</strong> Voice Selection (dropdown), "Clone New Voice" Button, Reference Audio Upload (Drag &amp; Drop).</li>
                                <li><strong>Middle Area (Control &amp; Fine-tuning):</strong> Parameter Control (Sliders/Dials with Real-time Feedback for: "Emotional Intensity", "Speaking Rate", "Pitch / Prosody", "Emphasis", "Volume"), Emotion Presets (Buttons for: "Happy," "Sad," "Angry," "Neutral," "Excited," "Whispering," <strong>"Commercial"</strong>, <strong>"Professional"</strong>).</li>
                                <li><strong>Lower Area (Output &amp; Mini-Mastering):</strong> "Preview" Button, "Generate" Button, Output Waveform, Mini-Mastering Tools ("Normalize," "Trim," "basic Compression"), "Export" Button.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">C. Speech-to-Speech (STS) Module View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Conversion of existing speech recordings into a new voice/style.</li>
                                <li><strong>Layout:</strong> "Source-Input" area and "Target-Output" area.</li>
                                <li><strong>Left Area (Source-Input):</strong> Upload-Zone, Source Audio Player, Transcription Text Field (editable).</li>
                                <li><strong>Right Area (Target-Output):</strong> Target Voice Selection, Parameter Control (emotion, prosody), Generate Button &amp; Output.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">D. Text-to-SFX Module View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Generation of sound effects from text descriptions.</li>
                                <li><strong>Layout:</strong> Input area for text description and parameters, output area for preview and export.</li>
                                <li><strong>Elements:</strong> Text Input Field (SFX descriptions), SFX Type/Style Selection, Parameter Adjustments (duration, intensity, pitch shifts), Generate Button &amp; Output, Export Options.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">E. Text-to-Music Module View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Generation of music tracks from text descriptions and musical parameters.</li>
                                <li><strong>Layout:</strong> Input area for text description and music parameters, output area for preview and export.</li>
                                <li><strong>Elements:</strong> Text Input Field (music descriptions), Genre/Mood Selection, Parameter Adjustments (tempo, key, duration, intensity/dynamics, complexity), Generate Button &amp; Output, Export Options.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">F. Voice Finder View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Allows users to find suitable voices from the library based on textual descriptions of desired characteristics or a sample text.</li>
                                <li><strong>Layout:</strong> Input area for description/sample text, search trigger, dynamic results display.</li>
                                <li><strong>Elements:</strong> Text Input Field, Search Button, Results Display (list/grid of matching voices, Voice Name, short description, Match Confidence Score, "Preview" and "Select" buttons).</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">G. Batch Processing View</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Efficient generation of multiple dialogue lines or entire scripts across speech, SFX, and music.</li>
                                <li><strong>Layout:</strong> Script editor area and a "Batch Control" area.</li>
                                <li><strong>Upper Area (Script Editor):</strong> Large text field for line-by-line dialogues, Script Import/Export Buttons, Speaker/Sound/Music Assignment per Line (dropdowns, visual indicators).</li>
                                <li><strong>Lower Area (Batch Control &amp; Output):</strong> "Generate All" Button, Progress Indicator/Task Queue, Output Management (filename preview, save location).</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">V. Detailed Workspace Layout &amp; Views (High-Level Conceptualization for GUI Programmer)</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);">The GUI's main window will feature a persistent, three-part layout:</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">A. Top Bar (Global Header):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Purpose:</strong> Global actions and navigation.</li>
                                <li><strong>Mockup Elements:</strong> Left: "Project" Dropdown/Button (Text, Icon: Folder), Global Settings (Gear Icon), Help/Support (Question Mark Icon), (Optional: User Profile Icon), and Theme Toggle.</li>
                                <li><strong>Styling:</strong> `h-16`, `bg-gray-900/80 backdrop-blur-lg`, `sticky top-0 z-50 shadow-sm`, `border-b border-gray-800`.</li>
                                <li><strong>Functionality:</strong> Contains the Dark/Light Mode toggle, which uses CSS variables and Local Storage for persistence.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">B. Left Sidebar (Navigation &amp; Project Explorer):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Description:</strong> A permanently visible and scrollable sidebar for project navigation and library access.</li>
                                <li><strong>Implementation:</strong> Implemented with `position: fixed; top: 120px; bottom: 0; left: 0; width: 250px; overflow-y: auto;`. This ensures it stays fixed alongside the main content and is independently scrollable.</li>
                                <li><strong>Mockup Elements:</strong> Top: "Project Explorer" (Tree View/List mock with clickable folders/files), "Recently Generated Audios" Panel (scrollable list with simulated detail dialogs on click), "Voice Library (Compact View)" (quick access to voices, expands to simulated "Voice Lab Wizard").</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">C. Central Workspace (Dynamic Main Content Area):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li><strong>Description:</strong> The largest and most dynamic area of the GUI, adapting its content based on the selected work mode (e.g., TTS, STS, Project View).</li>
                                <li><strong>Functionality:</strong> Houses the core functional modules, interactive elements, and detailed information displays.</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">VI. SSML Integration &amp; Model-Specific Behavior Hints</h2>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">SSML Tag Insertion Toolbar (Visual Representation within TTS Editor):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Functionality:</strong> Quick-access buttons to insert commonly used SSML tags.</li>
                                <li><strong>Key Tags (Coqui TTS XTTS v2 primary support):</strong> `&lt;speak&gt;`, `&lt;p&gt;`, `&lt;s&gt;`, `&lt;break time=&quot;duration&quot;/&gt;` or `&lt;break strength=&quot;level&quot;/&gt;`, `&lt;mark name=&quot;marker_name&quot;/&gt;`, `&lt;say-as interpret-as=&quot;type&quot;&gt;text&lt;/say-as&gt;`, `&lt;sub alias=&quot;substitution_text&quot;&gt;original_text&lt;/sub&gt;`, `&lt;phoneme alphabet=&quot;ipa&quot; ph=&quot;phonetic_transcription&quot;&gt;word&lt;/phoneme&gt;`, `&lt;prosody pitch=&quot;value&quot; rate=&quot;value&quot; volume=&quot;value&quot;&gt;text&lt;/prosody&gt;`, `&lt;emphasis level=&quot;strong&quot;&gt;text&lt;/emphasis&gt;`, `&lt;lang xml:lang=&quot;language_code&quot;&gt;text&lt;/lang&gt;`, `&lt;voice name=&quot;voice_id&quot;&gt;text&lt;/voice&gt;`.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Model-Specific Behavior Hints:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>XTTS v2:</strong> Full SSML support.</li>
                                <li><strong>Bark:</strong> Limited SSML (mostly pauses), relies on `[sound cues]` and textual cues.</li>
                                <li><strong>OpenVoice:</strong> Limited SSML (simple pauses).</li>
                                <li><strong>Whisper:</strong> STT model, SSML not applicable.</li>
                                <li><strong>Meta AudioCraft Suite (MusicGen &amp; AudioGen):</strong> Text-to-music/SFX, SSML not applicable, relies on direct text descriptions.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Unsupported SSML Tags (for user awareness):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li>Proprietary/Platform-Specific Tags (e.g., `&lt;mstts:express-as&gt;`, `&lt;amazon:emotion&gt;`, `&lt;sapi:rate&gt;`), `&lt;lexicon&gt;`, `&lt;audio&gt;`.</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">VII. Preferences and Customization</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);"><strong>Purpose:</strong> Allow users to adapt the system to individual or project-specific needs, enhancing efficiency, consistency, personalization, and professionalism.</p>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Structure:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>General/UI:</strong> Theme (Light/Dark mode toggle), GUI Language (EN/DE dropdown), Font Size/Scaling (slider/dropdown), Default Save Location (path selector).</li>
                                <li><strong>Project Management:</strong> Default Folder Structure (configurator), Auto-Save/Session Recovery (interval selector), Default Metadata for new projects.</li>
                                <li><strong>Audio Generation (Speech):</strong> Default Voice (TTS dropdown), Default Emotion Preset (dropdown), Default Model Variants (e.g., XTTS Lite/Standard/Large radio buttons), Quality Settings (Sample-Rate, Bit-Depth selectors).</li>
                                <li><strong>Audio Generation (SFX/Music):</strong> Default SFX/Music model variants, Default parameters for SFX/Music generation (e.g., default duration, tempo).</li>
                                <li><strong>Export:</strong> Default Export Path, Default File Format (WAV/MP3 radio buttons), Default Export Bitrate/Quality (MP3 slider).</li>
                                <li><strong>Dynamic File Naming Templates:</strong> Creation and management of named templates, Template Editor (selection and arrangement of placeholders), Live Preview of filename, Set Default.</li>
                                
                                <!-- Updated from here for correct display -->
                                <li><strong>Workflow &amp; Automation:</strong> Batch Processing Options (Speaker/Sound/Music assignment, error behavior), Default Post-Processing (Normalize, Trim toggles), Default Action after Generation, Automatic PDF Documentation Template.</li>
                                <li><strong>User Profile Management (If Multi-User):</strong> Specific user profiles with custom settings, Profile Export/Import.</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">VIII. Workflow Enhancers (Nice-to-Have Features)</h2>
                            <p class="mb-4" style="color: var(--color-text-primary);">These functions enhance user-friendliness and efficiency, prioritized after core features:</p>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li><strong>Real-time Preview:</strong> Fast, partial synthesis preview before final generation.</li>
                                <li><strong>Session Recovery / Autosave:</strong> Resume state after crashes or power loss.</li>
                                <li><strong>Voice Tagging &amp; Metadata:</strong> Add tags to voice models ("warm", "deep", "fast").</li>
                                <li><strong>Undo / Redo Stack:</strong> Allows undoing or redoing recent settings or parameter changes.</li>
                                <li><strong>Background Processing:</strong> Task queue with ETA and progress per item, notifications upon completion.</li>
                                <li><strong>Keyboard Shortcuts:</strong> For power users: Trigger actions via keys (e.g., CTRL+S, CTRL+ENTER).</li>
                                <li><strong>Multi-User / Login (Phase 3+):</strong> Admin, editor, and viewer roles, personalized GUI settings per user.</li>
                                <li><strong>Integration Points (Future):</strong> "Send to Editor" button for direct API integration with AVID Pro Tools, Adobe Premiere Pro, and DaVinci Resolve.</li>
                                <li><strong>Smart Error Handling:</strong> Human-readable messages + suggestions, link to troubleshooting tips/logs.</li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">IX. Technical Implementation Notes &amp; Deliverables Summary (Phased Roadmap)</h2>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Backend Framework (Initial):</strong> Built in Python (Gradio preferred for Phase 1).</li>
                                <li><strong>Operation Mode:</strong> Must run offline.</li>
                                <li><strong>Export Formats:</strong> WAV, SSML, JSON.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">Deliverables Summary (Phased Roadmap - Visualized by Chart.js Horizontal Bar Chart):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li><strong>Phase 1:</strong> (Focus: Core GUI, Speech Generation, Basic Project Management)
                                    <ul>
                                        <li>Fully functional GUI (TTS, V-Cloning, STS, Dashboard, Batch Processing for Speech).</li>
                                        <li>SSML Tag Toolbar.</li>
                                        <li>Voice control sliders.</li>
                                        <li>Reference voice upload.</li>
                                        <li>Audio export (WAV, MP3).</li>
                                        <li>Basic project management and settings.</li>
                                    </ul>
                                </li>
                                <li><strong>Phase 2:</strong> (Focus: Expanding Generative Capabilities, Workflow Enhancements, Initial Integration)
                                    <ul>
                                        <li>Integration of Text-to-SFX Module.</li>
                                        <li>Integration of Text-to-Music Module.</li>
                                        <li>Integration of <strong>Voice Finder</strong> module.</li>
                                        <li>Real-time Preview (for all generation types).</li>
                                        <li>Session Recovery / Autosave.</li>
                                        <li>Voice Tagging &amp; Metadata (extended for SFX/Music).</li>
                                        <li>Undo / Redo Stack.</li>
                                        <li>Background Processing, keyboard shortcuts, Smart Error Handling.</li>
                                        <li>Initial integration with AVID Pro Tools API (if technically feasible).</li>
                                        <li>Extended project management (history, Smart Versioning, search/filter), automatic PDF documentation.</li>
                                    </ul>
                                </li>
                                <li><strong>Phase 3:</strong> (Focus: Advanced Features, Multi-User, Full Integration)
                                    <ul>
                                        <li>Multi-User / Login.</li>
                                        <li>Full Integration Points with <strong>AVID Pro Tools API</strong> (and potentially other editors).</li>
                                        <li>Further advanced features based on user feedback.</li>
                                        <li>Comprehensive quality control functions.</li>
                                    </ul>
                                </li>
                            </ul>

                            <h2 class="text-2xl font-bold mb-4" style="color: var(--color-text-primary);">X. Technical Constraints &amp; Visualizations</h2>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">A. Core SPA Technical Requirements (Reinforced):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>Single HTML file ONLY.</strong></li>
                                <li><strong>No external CSS files</strong> (Tailwind via CDN).</li>
                                <li><strong>No external JavaScript files</strong> (except Chart.js/Plotly.js via CDN). Vanilla JS for all core logic.</li>
                                <li><strong>No `alert()` or `confirm()` prompts.</strong> Use custom modal UI or in-app messages.</li>
                                <li><strong>No HTML comments, CSS comments, or JavaScript comments</strong> in final code, <em>except</em> for the specified placeholder comments at the beginning of the HTML file.</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">B. Graphics &amp; Visualization (Reinforced):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li><strong>NO SVG graphics used.</strong></li>
                                <li><strong>NO Mermaid JS used.</strong></li>
                                <li><strong>NO raster images (PNG, JPEG, GIF, etc.).</strong></li>
                                <li><strong>Charts:</strong> Use `Chart.js` for standard dynamic charts (e.g., phases chart, bar charts).
                                    <ul class="list-circle list-inside ml-4">
                                        <li>Ensure `maintainAspectRatio: false` in Chart.js options.</li>
                                        <li>Labels must wrap approximately every 16 characters within tooltips.</li>
                                        <li>Dynamically updatable.</li>
                                        <li>Load via CDN.</li>
                                    </ul>
                                </li>
                                <li><strong>Plots (Optional):</strong> If highly sophisticated interactive plots are required beyond Chart.js, use `Plotly.js` (Canvas/WebGL only). AVOID Plotly SVG rendering.</li>
                                <li><strong>Icons &amp; Diagrams:</strong> Use <strong>Unicode characters/emojis</strong>, or <strong>structured HTML/CSS with Tailwind</strong> for visual elements, icons, and conceptual diagrams (e.g., the Workspace Layout).</li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">C. Chart Container Styling (CRITICAL for Layout Stability):</h3>
                            <ul class="list-disc list-inside space-y-1 mb-4" style="color: var(--color-text-primary);">
                                <li>For all `&lt;canvas&gt;` elements used by Chart.js or Plotly.js, they <strong>must</strong> be wrapped in a `&lt;div&gt;` element (e.g., `&lt;div class=&quot;chart-container&quot;&gt;`). This container `&lt;div&gt;` must be styled to:
                                    <ul class="list-circle list-inside ml-4">
                                        <li><strong>Occupy Full Parent Width:</strong> `w-full` (Tailwind).</li>
                                        <li><strong>Have a Maximum Width:</strong> A `max-width` (e.g., `max-w-xl`, `max-w-2xl` from Tailwind, or a custom class with embedded CSS like `max-width: 900px;`). This prevents charts from becoming excessively wide on large screens.</li>
                                        <li><strong>Be Centered Horizontally:</strong> If `max-width` is less than parent, apply `mx-auto` (Tailwind).</li>
                                        <li><strong>Have Controlled Height:</strong> A defined responsive height (e.g., `h-[40vh]` or `h-96`) and a `max-height` (e.g., `max-h-[400px]`). Adjust heights for mobile (e.g., `sm:h-80 md:h-96`).</li>
                                        <li><strong>Prevent Overflow:</strong> The container must strictly constrain the chart canvas. `position: relative;` is recommended for child element positioning (like tooltips).</li>
                                    </ul>
                                </li>
                            </ul>
                            <h3 class="text-xl font-bold mb-3" style="color: var(--color-text-primary);">D. Content Population &amp; Context:</h3>
                            <ul class="list-disc list-inside space-y-1 mb-6" style="color: var(--color-text-primary);">
                                <li>All textual content, data points, findings, and descriptions must be extracted directly from the "Structured GUI Briefing &ndash; AI Voice Production System" document.</li>
                                <li><strong>Every interactive element and visualization MUST have clear context within the application's designed structure.</strong> Explain what it shows (linking back to report concepts), how to interact, and the key takeaways it provides.</li>
                                <li><strong>Each major section of the DESIGNED application MUST have an introductory paragraph.</strong> This paragraph will explain the purpose of that section within the app, what kind of information/interactions the user will find there (referencing the source content it contains), and how it contributes to understanding the report's overall message.</li>
                            </ul>
                        </div>
                    </section>

                </div>
            </main>
        </div>
    </div>

    <!-- Modals (Example: for Confirmations or Alerts) -->
    <div id="customModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header"></div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="hideModal()">OK</button>
            </div>
        </div>
    </div>


    <script>
        let phasesChartInstance; // Keep track of Chart instance

        document.addEventListener('DOMContentLoaded', function() {
            // Project Dropdown Logic in the Top Bar
            const projectDropdownBtn = document.getElementById('projectDropdownBtn');
            const projectDropdownContent = document.getElementById('projectDropdownContent');
            projectDropdownBtn.addEventListener('click', function() {
                projectDropdownContent.classList.toggle('hidden');
            });
            // Hide dropdown when clicking outside
            window.addEventListener('click', function(event) {
                if (!projectDropdownBtn.contains(event.target) && !projectDropdownContent.contains(event.target)) {
                    projectDropdownContent.classList.add('hidden');
                }
            });

            // Theme Toggle Logic
            const themeToggleBtn = document.getElementById('themeToggle');
            const body = document.body;

            // Helper function to get RGB values from hex
            function hexToRgb(hex) {
                const bigint = parseInt(hex.slice(1), 16);
                const r = (bigint >> 16) & 255;
                const g = (bigint >> 8) & 255;
                const b = bigint & 255;
                return `${r}, ${b}, ${g}`; /* Switched G and B to match desired blue tone more accurately */
            }

            // Function to set theme
            function setTheme(isDarkMode) {
                if (isDarkMode) {
                    body.classList.remove('light-mode');
                    localStorage.setItem('theme', 'dark');
                } else {
                    body.classList.add('light-mode');
                    localStorage.setItem('theme', 'light');
                }
                updateThemeIcon(isDarkMode);
                updateChartColors(); // Update chart colors on theme change
                updateCssVariableRgbValues(); // Update RGB for backdrop-blur
            }

            // Function to update theme icon
            function updateThemeIcon(isDarkMode) {
                const iconSpan = themeToggleBtn.querySelector('span');
                iconSpan.textContent = isDarkMode ? '💡' : '🌙'; // Lightbulb for dark mode, Moon for light mode
            }

            // Function to update RGB CSS variables for backdrop-blur
            function updateCssVariableRgbValues() {
                const root = document.documentElement;
                // Get computed styles to retrieve the actual hex values from the CSS variables
                const computedStyle = getComputedStyle(root);

                const primaryBgHex = computedStyle.getPropertyValue('--color-bg-primary').trim();
                const secondaryBgHex = computedStyle.getPropertyValue('--color-bg-secondary').trim();
                let accentBlueDarkHex = computedStyle.getPropertyValue('--color-accent-blue-dark').trim();

                // Fallback and ensure it's a hex before conversion for robustness
                if (!accentBlueDarkHex || !accentBlueDarkHex.startsWith('#') || accentBlueDarkHex.length !== 7) {
                    accentBlueDarkHex = body.classList.contains('light-mode') ? '#EAF2F8' : '#2C5282';
                }

                root.style.setProperty('--color-bg-primary-rgb', hexToRgb(primaryBgHex));
                root.style.setProperty('--color-bg-secondary-rgb', hexToRgb(secondaryBgHex));
                root.style.setProperty('--color-accent-blue-rgb', hexToRgb(accentBlueDarkHex));
            }

            // Check saved theme preference on load
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                setTheme(false); // Apply light mode
            } else {
                setTheme(true); // Default to dark mode if no preference or 'dark'
            }

            // Event listener for toggle button
            themeToggleBtn.addEventListener('click', () => {
                setTheme(!body.classList.contains('light-mode')); // Toggle theme
            });

            // Function to set active tab and scroll to the section
            window.setActiveTabWithScroll = function(tabId, sectionId = 'core-features') {
                // Deactivate all current tabs
                document.querySelectorAll('#core-features .tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                // Deactivate all tab buttons
                document.querySelectorAll('#core-features .tab-button').forEach(button => {
                    button.classList.remove('active');
                });

                // Activate the selected tab content
                const selectedTabContent = document.getElementById(tabId);
                if (selectedTabContent) {
                    selectedTabContent.classList.add('active');
                }

                // Activate the corresponding tab button in the main tab navigation
                const selectedTabButton = document.querySelector(`#core-features button[onclick*="${tabId}"]`);
                if (selectedTabButton) {
                    selectedTabButton.classList.add('active');
                }

                // Scroll to the target section
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            };

            // Slider Value Display Logic
            const sliders = [
                { id: 'emotionIntensity', valueSpanId: 'emotionValue', suffix: '%' },
                { id: 'speakingRate', valueSpanId: 'rateValue', suffix: 'x' },
                { id: 'pitchProsody', valueSpanId: 'pitchValue', suffix: '' },
                { id: 'emphasis', valueSpanId: 'emphasisValue', suffix: '%' },
                { id: 'volume', valueSpanId: 'volumeValue', suffix: '%' },
                { id: 'stsEmotionIntensity', valueSpanId: 'stsEmotionValue', suffix: '%' },
                { id: 'stsPitchProsody', valueSpanId: 'stsPitchValue', suffix: '' },
                { id: 'sfxDuration', valueSpanId: 'sfxDurationValue', suffix: '' },
                { id: 'sfxIntensity', valueSpanId: 'sfxIntensityValue', suffix: '%' },
                { id: 'sfxPitch', valueSpanId: 'sfxPitchValue', suffix: '' },
                { id: 'musicTempo', valueSpanId: 'musicTempoValue', suffix: '' },
                { id: 'musicDuration', valueSpanId: 'musicDurationValue', suffix: '' },
                { id: 'musicIntensity', valueSpanId: 'musicIntensityValue', suffix: '%' }
            ];

            sliders.forEach(sliderInfo => {
                const slider = document.getElementById(sliderInfo.id);
                const valueSpan = document.getElementById(sliderInfo.valueSpanId);
                if (slider && valueSpan) {
                    valueSpan.textContent = slider.value + sliderInfo.suffix;
                    slider.oninput = function() {
                        valueSpan.textContent = this.value + sliderInfo.suffix;
                    };
                }
            });

            // Emotion Presets Logic for TTS module
            window.applyPreset = function(presetName) {
                let emotionValue, rateValue, pitchValue, emphasisValue, volumeValue;

                switch(presetName) {
                    case 'happy':
                        emotionValue = 80; rateValue = 1.1; pitchValue = 2; emphasisValue = 70; volumeValue = 100;
                        break;
                    case 'sad':
                        emotionValue = 20; rateValue = 0.8; pitchValue = -2; emphasisValue = 30; volumeValue = 80;
                        break;
                    case 'angry':
                        emotionValue = 90; rateValue = 1.2; pitchValue = 3; emphasisValue = 80; volumeValue = 110;
                        break;
                    case 'neutral':
                        emotionValue = 50; rateValue = 1.0; pitchValue = 0; emphasisValue = 50; volumeValue = 100;
                        break;
                    case 'excited':
                        emotionValue = 95; rateValue = 1.3; pitchValue = 4; emphasisValue = 90; volumeValue = 115;
                        break;
                    case 'whispering':
                        emotionValue = 30; rateValue = 0.7; pitchValue = -1; emphasisValue = 40; volumeValue = 50;
                        break;
                    case 'commercial':
                        emotionValue = 75; rateValue = 1.15; pitchValue = 1; emphasisValue = 85; volumeValue = 105;
                        break;
                    case 'professional':
                        emotionValue = 60; rateValue = 1.05; pitchValue = 0.5; emphasisValue = 60; volumeValue = 95;
                        break;
                    default: // Neutral fallback
                        emotionValue = 50; rateValue = 1.0; pitchValue = 0; emphasisValue = 50; volumeValue = 100;
                }

                document.getElementById('emotionIntensity').value = emotionValue;
                document.getElementById('emotionValue').textContent = emotionValue + '%';
                document.getElementById('speakingRate').value = rateValue;
                document.getElementById('rateValue').textContent = rateValue + 'x';
                document.getElementById('pitchProsody').value = pitchValue;
                document.getElementById('pitchValue').textContent = pitchValue;
                document.getElementById('emphasis').value = emphasisValue;
                document.getElementById('emphasisValue').textContent = emphasisValue + '%';
                document.getElementById('volume').value = volumeValue;
                document.getElementById('volumeValue').textContent = volumeValue + '%';
            };

            // Drag & Drop Zone Feedback (simulated upload)
            const dropZones = ['textDropZone', 'audioDropZone', 'stsAudioDropZone'];
            dropZones.forEach(zoneId => {
                const zone = document.getElementById(zoneId);
                if (zone) {
                    zone.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        this.classList.add('drag-over');
                    });
                    zone.addEventListener('dragleave', function(e) {
                        e.preventDefault();
                        this.classList.remove('drag-over');
                    });
                    zone.addEventListener('drop', function(e) {
                        e.preventDefault();
                        this.classList.remove('drag-over');
                        showCustomModal('File Uploaded', 'The file "' + e.dataTransfer.files[0].name + '" has been successfully simulated uploaded.');
                        // In a real application, you would handle the actual file upload logic here.
                    });
                    zone.addEventListener('click', function() {
                        showCustomModal('File Selection', 'File selection dialog simulated. In a real app, a file browser would open here.');
                    });
                }
            });

            // Custom Modal Functions (replaces alert/confirm for better UX)
            const customModal = document.getElementById('customModal');
            const modalHeader = customModal.querySelector('.modal-header');
            const modalBody = customModal.querySelector('.modal-body');

            window.showCustomModal = function(headerText, bodyText) {
                modalHeader.textContent = headerText;
                modalBody.textContent = bodyText;
                customModal.style.display = 'flex'; // Show modal
            };

            window.hideModal = function() {
                customModal.style.display = 'none'; // Hide modal
            };

            // Voice Finder Logic (Simulated search and results display)
            window.findVoice = function() {
                const description = document.getElementById('voiceDescriptionInput').value;
                const resultsDiv = document.getElementById('voiceResults');
                resultsDiv.innerHTML = ''; // Clear previous results

                if (description.trim() === '') {
                    showCustomModal('Hint', 'Please enter a description or sample text to find voices.');
                    return;
                }

                // Simulate search results based on input or random
                const simulatedResults = [
                    { name: 'Cloned Voice: Sarah (Warm)', desc: 'Ideal for soothing content.', score: '92%' },
                    { name: 'Standard Male Voice 1', desc: 'Clear and concise.', score: '78%' },
                    { name: 'Cloned Voice: Anna (Friendly)', desc: 'Optimistic and inviting.', score: '85%' },
                    { name: 'Deep Narrator Voice', desc: 'Authoritative and resonant, perfect for documentaries.', score: '95%' }
                ];

                // Shuffle results to give a dynamic feel on repeated searches
                simulatedResults.sort(() => 0.5 - Math.random());

                simulatedResults.forEach(voice => {
                    const resultElement = document.createElement('div');
                    resultElement.className = 'p-3 rounded-md flex items-center justify-between shadow-sm border';
                    resultElement.style.backgroundColor = 'var(--color-bg-list-item)';
                    resultElement.style.borderColor = 'var(--color-border-primary)';
                    resultElement.innerHTML = `
                        <div>
                            <span class="font-semibold" style="color: var(--color-text-primary);">${voice.name}</span>
                            <p class="text-sm" style="color: var(--color-text-secondary);">${voice.desc}</p>
                        </div>
                        <div class="flex space-x-2">
                            <span class="font-bold text-lg ${parseFloat(voice.score) > 90 ? 'text-green-400' : parseFloat(voice.score) > 70 ? 'text-yellow-400' : 'text-orange-400'}">${voice.score}</span>
                            <button class="text-blue-400 hover:text-blue-300 text-lg" onclick="showCustomModal('Playback', 'Playing sample for &quot;${voice.name}&quot;...')">▶️</button>
                            <button class="btn-secondary text-sm py-1 px-3" onclick="showCustomModal('Selected', 'Voice &quot;${voice.name}&quot; selected!')">Select</button>
                        </div>
                    `;
                    resultsDiv.appendChild(resultElement);
                });
            };

            // Chart.js for Phases Roadmap visualization
            function initializeChart() {
                const ctx = document.getElementById('phasesChart').getContext('2d');
                if (phasesChartInstance) {
                    phasesChartInstance.destroy(); // Destroy old chart instance if it exists
                }
                phasesChartInstance = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Phase 1: Core GUI & Speech', 'Phase 2: Expanded Capabilities & Enhancements', 'Phase 3: Advanced & Multi-User'],
                        datasets: [{
                            label: 'Development Progress',
                            data: [100, 60, 20], // Simulated progress
                            backgroundColor: [
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-bar-color-1'),
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-bar-color-2'),
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-bar-color-3')
                            ],
                            borderColor: [
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-border-color-1'),
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-border-color-2'),
                                getComputedStyle(document.documentElement).getPropertyValue('--chart-border-color-3')
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.x !== null) {
                                            label += context.parsed.x + '%';
                                        }
                                        const words = label.split(' ');
                                        let lines = [];
                                        let currentLine = '';
                                        words.forEach(word => {
                                            if ((currentLine + word).length > 16 && currentLine.length > 0) {
                                                lines.push(currentLine);
                                                currentLine = word;
                                            } else {
                                                currentLine += (currentLine === '' ? '' : ' ') + word;
                                            }
                                        });
                                        lines.push(currentLine);
                                        return lines;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Progress (%)',
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-axis-text')
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    },
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-axis-text')
                                },
                                grid: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-grid-line')
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Phases',
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-axis-text')
                                },
                                ticks: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-axis-text')
                                },
                                grid: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--chart-grid-line')
                                }
                            }
                        }
                    }
                });
            }

            // Function to update chart colors based on current theme
            function updateChartColors() {
                if (!phasesChartInstance) {
                    initializeChart(); // Initialize if not already
                    return;
                }

                const rootStyle = getComputedStyle(document.documentElement);

                phasesChartInstance.data.datasets[0].backgroundColor = [
                    rootStyle.getPropertyValue('--chart-bar-color-1'),
                    rootStyle.getPropertyValue('--chart-bar-color-2'),
                    rootStyle.getPropertyValue('--chart-bar-color-3')
                ];
                phasesChartInstance.data.datasets[0].borderColor = [
                    rootStyle.getPropertyValue('--chart-border-color-1'),
                    rootStyle.getPropertyValue('--chart-border-color-2'),
                    rootStyle.getPropertyValue('--chart-border-color-3')
                ];
                phasesChartInstance.options.scales.x.title.color = rootStyle.getPropertyValue('--chart-axis-text');
                phasesChartInstance.options.scales.x.ticks.color = rootStyle.getPropertyValue('--chart-axis-text');
                phasesChartInstance.options.scales.x.grid.color = rootStyle.getPropertyValue('--chart-grid-line');
                phasesChartInstance.options.scales.y.title.color = rootStyle.getPropertyValue('--chart-axis-text');
                phasesChartInstance.options.scales.y.ticks.color = rootStyle.getPropertyValue('--chart-axis-text');
                phasesChartInstance.options.scales.y.grid.color = rootStyle.getPropertyValue('--chart-grid-line');

                phasesChartInstance.update();
            }

            initializeChart(); // Initial chart rendering
            updateChartColors(); // Set initial colors based on current theme
            updateCssVariableRgbValues(); // Set initial RGB variables
        });
    </script>
</body>
</html>
