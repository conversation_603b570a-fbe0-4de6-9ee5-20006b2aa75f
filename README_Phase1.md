# AI Audio System - Phase 1 Demo

This is the **Phase 1 implementation** of the AI Audio System, featuring a Streamlit-based GUI for Speech-to-Speech conversion with voice cloning capabilities.

## 🎯 Phase 1 Features

- **Speech-to-Text**: Upload audio files and transcribe them using Whisper (large-v3)
- **Text Editing**: Edit transcribed text before speech generation
- **Voice Cloning**: Upload reference audio to clone voice characteristics
- **Text-to-Speech**: Generate speech using Coqui TTS (XTTS v2)
- **Multi-language Support**: Support for 15+ languages
- **Audio Playback & Download**: Play and download generated audio files

## 🔧 System Requirements

- **Python**: 3.8 or higher
- **GPU**: NVIDIA GPU with CUDA support (recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 5GB free space for models

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

1. **Clone or download** this project
2. **Navigate** to the project directory
3. **Run the setup script**:
   ```bash
   python setup_phase1.py
   ```
4. **Start the application**:
   ```bash
   streamlit run streamlit_app.py
   ```

### Option 2: Manual Setup

1. **Install FFmpeg**:
   - Windows: Download from [ffmpeg.org](https://ffmpeg.org/download.html)
   - macOS: `brew install ffmpeg`
   - Linux: `sudo apt-get install ffmpeg`

2. **Install Python dependencies**:
   ```bash
   pip install --upgrade pip
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   streamlit run streamlit_app.py
   ```

## 📖 How to Use

### Step 1: Load Models
1. Open the application in your browser
2. Use the sidebar to click "🔄 Load Models"
3. Wait for both TTS and Whisper models to load

### Step 2: Upload Audio
1. Upload an audio file in the "📤 Input Audio" section
2. Click "🎯 Transcribe Audio" to convert speech to text

### Step 3: Prepare Reference Voice
1. Upload a reference audio file (3-10 seconds recommended)
2. This will be used for voice cloning

### Step 4: Edit Text
1. Review and edit the transcribed text if needed
2. Or enter text directly if you didn't upload audio

### Step 5: Generate Speech
1. Select the target language
2. Click "🚀 Generate Speech"
3. Wait for the audio generation to complete

### Step 6: Download Results
1. Play the generated audio
2. Download the result using the "💾 Download Audio" button

## 🎙️ Supported Audio Formats

- **Input**: WAV, MP3, FLAC, M4A
- **Output**: WAV format
- **Reference Voice**: WAV, MP3, FLAC

## 🌍 Supported Languages

- English (en)
- German (de)
- Spanish (es)
- French (fr)
- Italian (it)
- Portuguese (pt)
- Polish (pl)
- Turkish (tr)
- Russian (ru)
- Dutch (nl)
- Czech (cs)
- Arabic (ar)
- Chinese (zh-cn)
- Japanese (ja)
- Hungarian (hu)
- Korean (ko)

## 🔍 Troubleshooting

### Common Issues

1. **CUDA not available**:
   - Install NVIDIA drivers and CUDA toolkit
   - Or use CPU mode (slower but functional)

2. **Model loading fails**:
   - Check internet connection for initial model download
   - Ensure sufficient disk space (5GB+)

3. **Audio upload issues**:
   - Ensure audio files are in supported formats
   - Check file size (recommend < 100MB)

4. **Generation takes too long**:
   - Use shorter text inputs
   - Ensure CUDA is available for GPU acceleration

### Performance Tips

- **Use GPU**: Ensure CUDA is available for faster processing
- **Shorter audio**: Use 3-10 second reference audio clips
- **Clean audio**: Use high-quality, noise-free reference audio
- **Appropriate text length**: Keep text under 500 characters for faster generation

## 📁 Project Structure

```
voice_project/
├── streamlit_app.py          # Main Streamlit application
├── setup_phase1.py           # Automated setup script
├── requirements.txt          # Python dependencies
├── README_Phase1.md          # This file
├── main_1.py                 # Original voice generation script
└── Briefing documents/       # Project documentation
```

## 🎯 Phase 1 Goals Achieved

✅ **Core AI Integration**: Coqui TTS (XTTS v2) and Whisper  
✅ **Intuitive GUI**: Simple Streamlit interface  
✅ **Speech-to-Speech Workflow**: Complete STS pipeline  
✅ **Voice Cloning**: Reference audio-based voice cloning  
✅ **Multi-language Support**: 15+ languages supported  
✅ **Audio Processing**: Upload, process, and download audio  

## 🔮 Next Steps (Phase 2)

- Advanced GUI with React.js frontend
- Additional AI models (Bark, AudioCraft)
- Batch processing capabilities
- Project management features
- Enhanced voice library
- Professional workflow optimizations

## 📞 Support

For technical issues or questions about this Phase 1 implementation, please refer to the project briefing documents or contact the development team.

---

**Note**: This is a Phase 1 demonstration system. The full production system will include additional features and optimizations as outlined in the project briefing documents.
