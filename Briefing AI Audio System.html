<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Briefing AI Audio System_v01</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* CSS Variables for Theming */
        :root { /* Dark Mode Defaults */
            --color-bg-primary: #0F0F0F; /* Overall very dark background */
            --color-bg-secondary: #1A1A1A; /* Cards, main content background */
            --color-bg-tertiary: #2D2D2D; /* Inputs, tab button background */
            --color-bg-light: #3A3A3A; /* Hover states, secondary buttons */
            --color-bg-list-item: #1F1F1F; /* Specific list item background */
            --color-bg-welcome-gradient-from: #1A1A1A; /* Not used in this version but kept for consistency with palette */
            --color-bg-welcome-gradient-to: #2D2D2D; /* Not used in this version but kept for consistency with palette */

            --color-text-primary: #E0E0E0; /* Main text */
            --color-text-secondary: #A0AEC0; /* Secondary text, labels */
            --color-text-accent: #63B3ED; /* Headings, primary icons */
            --color-text-link: #4299E1; /* Links */

            --color-border-primary: #3A3A3A; /* General element borders */
            --color-border-secondary: #4A4A4A; /* Input borders */

            --color-accent-blue: #63B3ED; /* Primary interactive blue */
            --color-accent-blue-hover: #4299E1;
            --color-accent-blue-dark: #2C5282; /* Used for darker accents like project info box */

            --shadow-sm: rgba(0, 0, 0, 0.1);
            --shadow-md: rgba(0, 0, 0, 0.2);
            --shadow-lg: rgba(0, 0, 0, 0.3);
            --shadow-xl: rgba(0, 0, 0, 0.4);

            --chart-bar-color-1: rgb(59, 130, 246); /* Blue-500 for completed */
            --chart-bar-color-2: rgb(96, 165, 250); /* Blue-400 for in-progress */
            --chart-bar-color-3: rgb(156, 163, 175); /* Gray-400 for future */
            --chart-border-color-1: rgb(37, 99, 235);
            --chart-border-color-2: rgb(59, 130, 246);
            --chart-border-color-3: rgb(107, 114, 128);
            --chart-axis-text: #A0AEC0;
            --chart-grid-line: #4A4A4A;
        }

        body.light-mode {
            --color-bg-primary: #F0F0F0;
            --color-bg-secondary: #FFFFFF;
            --color-bg-tertiary: #EAEAEA;
            --color-bg-light: #DCDCDC;
            --color-bg-list-item: #FDFDFD;
            --color-bg-welcome-gradient-from: #FDFDFD;
            --color-bg-welcome-gradient-to: #EAEAEA;


            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-accent: #007AFF; /* Apple Blue */
            --color-text-link: #0056B3;

            --color-border-primary: #E0E0E0;
            --color-border-secondary: #D0D0D0;

            --color-accent-blue: #007AFF;
            --color-accent-blue-hover: #0056B3;
            --color-accent-blue-dark: #EAF2F8; /* Light blue accent box for light mode */

            --shadow-sm: rgba(0, 0, 0, 0.03);
            --shadow-md: rgba(0, 0, 0, 0.05);
            --shadow-lg: rgba(0, 0, 0, 0.1);
            --shadow-xl: rgba(0, 0, 0, 0.15);

            --chart-bar-color-1: rgb(0, 122, 255);
            --chart-bar-color-2: rgb(102, 178, 255);
            --chart-bar-color-3: rgb(190, 190, 190);
            --chart-border-color-1: rgb(0, 100, 200);
            --chart-border-color-2: rgb(80, 150, 220);
            --chart-border-color-3: rgb(150, 150, 150);
            --chart-axis-text: #666666;
            --chart-grid-line: #D0D0D0;
        }

        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: var(--color-bg-primary);
            color: var(--color-text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        html {
            height: 100%;
        }

        /* Custom scrollbar */
        body::-webkit-scrollbar { width: 8px; }
        body::-webkit-scrollbar-track { background: var(--color-bg-tertiary); border-radius: 10px; }
        body::-webkit-scrollbar-thumb { background: var(--color-bg-light); border-radius: 10px; }
        body::-webkit-scrollbar-thumb:hover { background: var(--color-text-secondary); }

        /* Fixed Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px; /* h-16 */
            background-color: rgba(var(--color-bg-primary-rgb), 0.8);
            backdrop-filter: blur(10px);
            z-index: 50;
            box-shadow: 0 1px 2px var(--shadow-sm);
            border-bottom: 1px solid var(--color-border-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem; /* px-4 */
        }
        header .font-semibold { color: var(--color-text-primary); }
        header .text-gray-400 { color: var(--color-text-secondary); }
        header button:hover { background-color: var(--color-bg-tertiary); }
        #projectDropdownContent {
            background-color: var(--color-bg-secondary);
            box-shadow: 0 8px 16px var(--shadow-lg);
            border: 1px solid var(--color-border-primary);
        }
        #projectDropdownContent a { color: var(--color-text-primary); }
        #projectDropdownContent a:hover { background-color: var(--color-bg-tertiary); }

        /* Main Content Area: Left Sidebar + Central Workspace */
        .main-layout-container {
            display: flex;
            flex-grow: 1;
            margin-top: 64px; /* Offset for header (64px) */
            overflow: hidden;
        }

        /* Fixed Left Sidebar */
        aside {
            position: fixed;
            top: 64px; /* Below header */
            bottom: 0; /* Extends to bottom of viewport */
            left: 0;
            width: 250px;
            background-color: var(--color-bg-secondary);
            border-right: 1px solid var(--color-border-primary);
            padding: 1rem; /* p-4 */
            display: flex;
            flex-direction: column;
            gap: 1rem; /* space-y-4 */
            overflow-y: auto; /* Scrollable sidebar content */
            z-index: 30;
        }
        @media (max-width: 767px) { /* Hide sidebar on small screens */
            aside { display: none; }
        }

        /* Main Content Area (Scrollable) */
        main {
            flex-grow: 1;
            margin-left: 250px; /* Offset for fixed sidebar */
            background-color: var(--color-bg-primary);
            padding: 1rem 2rem; /* p-4 lg:p-8 */
            overflow-y: auto;
        }
        @media (max-width: 767px) { /* Adjust main content on small screens */
            main { margin-left: 0; }
        }

        .section-container {
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            display: flex;
            flex-direction: column;
            gap: 3rem; /* space-y-12 */
        }

        /* Styling for section headers */
        .section-header {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-text-accent);
            margin-bottom: 2rem;
            padding-top: 2rem; /* Adjusted padding-top for smooth scroll target */
            margin-top: -2rem; /* Counteract padding-top for smooth scroll target */
        }

        /* Standard text styles */
        p, ul, ol, li {
            color: var(--color-text-primary);
        }
        strong {
            color: var(--color-text-primary);
        }
        a {
            color: var(--color-text-link);
        }
        a:hover {
            text-decoration: underline;
        }

        /* Specific styles for lists, sections within the main content */
        section {
            background-color: var(--color-bg-secondary);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px var(--shadow-md);
            padding: 1.5rem 2rem;
            border: 1px solid var(--color-border-primary);
            margin-bottom: 3rem; /* Space between sections */
        }

        /* Specific overrides for the workspace mockup diagram (if any are still relevant) */
        .border.border-gray-700 { border-color: var(--color-border-primary); }
        .bg-blue-900 { background-color: var(--color-accent-blue-dark); }
        .bg-blue-950 { background-color: rgba(var(--color-accent-blue-rgb), 0.2); }
        .bg-gray-800 { background-color: var(--color-bg-tertiary); }
        .text-blue-200 { color: var(--color-text-accent); }
        .bg-blue-800 { background-color: var(--color-accent-blue-hover); }

    </style>
</head>
<body>
    <div class="h-screen flex flex-col">

        <!-- A. Top Bar (Global Header) - Fixed -->
        <header>
            <div class="relative">
                <button id="projectDropdownBtn" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">🗂️</span>
                    <span class="font-semibold text-gray-100">Project</span>
                    <span class="text-gray-400">▼</span>
                </button>
                <div id="projectDropdownContent" class="hidden absolute top-full left-0 mt-2 w-48 rounded-lg shadow-lg py-2 z-60" style="background-color: var(--color-bg-secondary); border: 1px solid var(--color-border-primary);">
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Create New Project</a>
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Open Project</a>
                    <a href="#" class="block px-4 py-2 text-gray-100 hover:bg-blue-900" style="color: var(--color-text-primary); --tw-bg-opacity: 0.1; background-color: rgba(var(--color-accent-blue-rgb), var(--tw-bg-opacity));">Switch Project</a>
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">💡</span>
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">⚙️</span>
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">❓</span>
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-800 transition-colors">
                    <span class="text-xl">👤</span>
                </button>
            </div>
        </header>

        <div class="main-layout-container">
            <!-- B. Left Sidebar (Navigation) - Fixed -->
            <aside>
                <div class="p-3 rounded-lg shadow-md" style="background-color: var(--color-bg-light); border: 1px solid var(--color-border-primary);">
                    <h3 class="font-bold text-md mb-2 flex items-center space-x-2" style="color: var(--color-text-primary);">
                        <span class="text-lg">🧭</span> Navigation
                    </h3>
                    <ul class="text-sm space-y-1" style="color: var(--color-text-secondary);">
                        <li><a href="#introduction" class="block py-1 hover:text-blue-400" onclick="scrollToSection('introduction')">Introduction</a></li>
                        <li><a href="#our-system" class="block py-1 hover:text-blue-400" onclick="scrollToSection('our-system')">1. Our System (Hardware)</a></li>
                        <li><a href="#core-functions" class="block py-1 hover:text-blue-400" onclick="scrollToSection('core-functions')">2. Core Functions of the Speech AI System</a></li>
                        <li><a href="#technological-focus" class="block py-1 hover:text-blue-400" onclick="scrollToSection('technological-focus')">3. Technological Focus</a></li>
                        <li><a href="#gui-considerations" class="block py-1 hover:text-blue-400" onclick="scrollToSection('gui-considerations')">3a. GUI Implementation Considerations</a></li>
                        <li><a href="#freelancer-services" class="block py-1 hover:text-blue-400" onclick="scrollToSection('freelancer-services')">4. Desired Freelancer Services</a></li>
                        <li><a href="#minimal-system" class="block py-1 hover:text-blue-400" onclick="scrollToSection('minimal-system')">5. Minimal System for Programmer Testing</a></li>
                        <li><a href="#profile-requirements" class="block py-1 hover:text-blue-400" onclick="scrollToSection('profile-requirements')">6. Freelancer Profile Requirements</a></li>
                        <li><a href="#project-framework" class="block py-1 hover:text-blue-400" onclick="scrollToSection('project-framework')">7. Project Framework & Offer</a></li>
                        <li><a href="#collaboration" class="block py-1 hover:text-blue-400" onclick="scrollToSection('collaboration')">8. Collaboration & Project Management</a></li>
                        <li class="mt-4 border-t border-gray-700 pt-2"><a href="ai_voice_gui_briefing.html" target="_blank" class="block py-1 hover:text-blue-400">
                            <span class="text-lg">🔗</span> Open Detailed GUI Briefing
                        </a></li>
                    </ul>
                </div>
            </aside>

            <!-- C. Central Workspace (Scrollable Main Content Area) -->
            <main>
                <div class="section-container">

                    <!-- NEW Section: Introduction / Overview -->
                    <section id="introduction">
                        <h2 class="section-header">Introduction: Vision, Goals &amp; Path</h2>
                        <p class="mb-4 text-primary">
                            This document outlines the overarching vision, strategic goals, and the conceptual path for implementing our AI Audio System. It serves as an overview of how this system could be built, and we are open to better suggestions regarding tools, workflows, or architectural approaches. Our primary goal is to develop a professional, stable system that meets these requirements and delivers high-quality output &ndash; easy to use, yet versatile to achieve the best results.
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>What the system should do:</strong> Provide a professional, locally operated Speech AI system for generating voice, sound effects, and music, primarily for advertising, dubbing, and dialogue creation.</li>
                            <li><strong>Where we want to go:</strong> Achieve an intuitive, efficient, and creatively empowering platform that hides AI complexity while offering powerful customization for audio professionals.</li>
                            <li><strong>How we get there:</strong> Through a phased development approach, prioritizing open-source solutions, robust local operation, and a collaborative development process that values expert technical input on architectural decisions, especially regarding the GUI.</li>
                        </ul>
                    </section>

                    <!-- Section: 1. Our System (Hardware) -->
                    <section id="our-system">
                        <h2 class="section-header">1. Our System (Hardware)</h2>
                        <p class="mb-4 text-primary">
                            Our system is configured as follows and is ready for installation:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Operating System:</strong> Microsoft Windows 11 Pro</li>
                            <li><strong>CPU:</strong> AMD Ryzen 9 7950X</li>
                            <li><strong>RAM:</strong> 64GB DDR5 6000-30</li>
                            <li><strong>GPU:</strong> ASUS TUF Gaming NVIDIA GeForce RTX 5090 32GB GDGD7 (with latest NVIDIA Studio driver installed)</li>
                            <li><strong>Storage:</strong> 2TB + 1TB Samsung 990 Pro M.2 NVMe SSDs</li>
                            <li><strong>Power Supply:</strong> be quiet! STRAIGHT POWER12 1000W ATX3.0 Platinum (with native 12V-2x6 / 12VHPWR connector)</li>
                        </ul>
                    </section>

                    <!-- Section: 2. Core Functions of the Speech AI System (Desired State) -->
                    <section id="core-functions">
                        <h2 class="section-header">2. Core Functions of the Speech AI System (Desired State)</h2>
                        <p class="mb-4 text-primary">
                            The system should provide the following main functionalities:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Voice Cloning:</strong> High-quality cloning of custom and existing voices.</li>
                            <li><strong>Text-to-Speech (TTS):</strong> Generation of speech from text.</li>
                            <li><strong>Speech-to-Speech (STS):</strong> Transformation of spoken language (transcribed via STT) into other voices or styles.</li>
                            <li><strong>Multilingual Localization:</strong> At least German and English.</li>
                            <li><strong>Fine-tuning of Voice Rendition:</strong> Precise control over emotions, speaking style, and prosody.</li>
                            <li><strong>Parallel Voices:</strong> Ability to process up to 3 parallel voices in an audio production.</li>
                            <li><strong>Output Format:</strong> Audio only.</li>
                            <li><strong>Text-to-SFX / Text-to-Music:</strong> Experimental generation of sound effects or music from text using AI.</li>
                            <li><strong>LIPSYNC:</strong> Support for speech synchronization to lip movements (e.g., via Wav2Lip). <span class="text-secondary text-sm"><em>(Note: This is an optional, absolute nice-to-have feature and may be realized in a later, unscheduled phase, not necessarily in Phase 2.)</em></span></li>
                        </ul>
                    </section>

                    <!-- Section: 3. Technological Focus -->
                    <section id="technological-focus">
                        <h2 class="section-header">3. Technological Focus</h2>
                        <p class="mb-4 text-primary">
                            We prioritize the implementation and integration of the following open-source tools and models. A **highly intuitive and fast workflow** for operating the toolkit is of central importance:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Core AI Libraries:</strong>
                                <ul class="list-circle list-inside ml-4">
                                    <li>Coqui TTS (specifically XTTS v2 model)</li>
                                    <li>OpenVoice</li>
                                    <li>Whisper</li>
                                    <li>Bark</li>
                                    <li>Meta AudioCraft Suite (MusicGen &amp; AudioGen): For dedicated and high-quality **Text-to-Music** as well as **Text-to-SFX** generation. These models may optionally utilize cloud resources.</li>
                                    <li>Wav2Lip <span class="text-secondary text-sm"><em>(Full integration of Wav2Lip will be prioritized in a later, unscheduled phase, as an optional nice-to-have feature.)</em></span></li>
                                </ul>
                            </li>
                            <li><strong>Programming Language:</strong> Python (latest stable 64-bit version, e.g., 3.10.x or 3.11.x)</li>
                            <li><strong>Machine Learning Framework:</strong> PyTorch (CUDA-enabled, optimized for RTX 5090)</li>
                            <li><strong>Development Environment/Tools:</strong> Git, FFmpeg, Visual Studio Build Tools</li>
                            <li><strong>Graphical User Interface (GUI):</strong> Primarily Gradio (for user-friendly, non-technical workflows). <span class="text-secondary text-sm"><em>(Note: While Gradio is the initial focus for rapid prototyping, the optimal framework for the final, polished GUI &ndash; potentially a dedicated frontend framework like React.js with a local backend server like Flask &ndash; is subject to the discussion in Section 3a. This dedicated approach is recommended for achieving the high aesthetic and interactive goals.)</em></span></li>
                        </ul>
                    </section>

                    <!-- Section: 3a. GUI Implementation Considerations -->
                    <section id="gui-considerations">
                        <h2 class="section-header">3a. GUI Implementation Considerations: Gradio/Streamlit vs. Dedicated Frontend Framework</h2>
                        <p class="mb-4 text-primary">
                            Regarding the Graphical User Interface (GUI) mentioned in Section 3, while Gradio and Streamlit are specified as primary tools for their ease of integrating with Python backends and rapid prototyping, it is crucial to address the scope and aesthetic ambitions of the desired GUI.
                        </p>
                        <p class="mb-4 text-primary">
                            The vision for this GUI extends beyond typical Gradio/Streamlit capabilities, aiming for a highly polished, interactive, and consistent user experience reminiscent of professional desktop applications (e.g., Apple macOS native apps or Avid Pro Tools). This includes:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Complex Fixed Layouts:</strong> Persistent header, fixed sidebars, and multi-panel interfaces that maintain their position and size independently of main content scrolling.</li>
                            <li><strong>Granular Styling &amp; Theming:</strong> Deep control over shadows, borders, precise spacing, and dynamic theme switching (Dark/Light Mode) affecting every UI element, without relying on broad CSS overrides that might break with framework updates.</li>
                            <li><strong>Rich Interactive Elements:</strong> Sophisticated dropdowns, interconnected components, and fluid visual feedback on interactions. This also includes elements like an SSML Tag Insertion Toolbar for precise text-to-speech control.</li>
                            <li><strong>Potential for Persistent UI State:</strong> The ability to save user-specific GUI settings, project notes, or progress within the interface itself (beyond what the backend models handle).</li>
                        </ul>
                        <h3 class="text-xl font-bold mt-6 mb-3 text-accent">Question to the Programmer:</h3>
                        <p class="mb-4 text-primary">
                            Given these advanced UI/UX requirements and the desire for a highly refined, performant, and intuitive user experience, we would like to open a discussion on whether Gradio or Streamlit are truly the optimal choices for the primary GUI implementation.
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li>Can Gradio/Streamlit effectively achieve the desired level of polish, layout complexity, and granular control without extensive, fragile workarounds?</li>
                            <li>Would a **dedicated frontend framework** such as **React.js, Angular, or Vue.js**, potentially combined with a **local backend server** (e.g., Python Flask/FastAPI running locally on the Windows server for API communication with the UI), be a more robust and scalable approach to fulfill these GUI aspirations? **This dedicated approach is our recommendation for achieving the high aesthetic and interactive goals of the final GUI.**</li>
                            <li>What are the **implications regarding development effort, performance, and long-term maintainability** for both approaches, considering the offline operational requirement for core AI functions?</li>
                        </ul>
                        <p class="mt-4 text-primary">
                            Your expert assessment and recommendations on this architectural decision are highly valued before proceeding with significant GUI development.
                        </p>
                    </section>
                    
                    <!-- Section: 4. Desired Freelancer Services -->
                    <section id="freelancer-services">
                        <h2 class="section-header">4. Desired Freelancer Services</h2>
                        <p class="mb-4 text-primary">
                            The freelancer is expected to assist us with the following tasks, which are divided into two main project phases:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Phase 1: Minimal System Core Setup (Test Gig)</strong> - Focus on foundational setup and proving core capabilities as defined in Section 5.</li>
                            <li><strong>Phase 2: Full System Implementation &amp; Advanced GUI</strong> - Building upon Phase 1 to include all remaining functionalities, advanced GUI features, and workflow optimizations.</li>
                        </ul>
                        <h3 class="text-xl font-bold mt-6 mb-3 text-accent">Detailed Tasks by Phase:</h3>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>System Review &amp; Optimization (Phase 1):</strong> Verification of correct hardware setup and drivers (NVIDIA Studio Driver, ReBAR/Above 4G Decoding in BIOS).</li>
                            <li><strong>Python Environment Setup (Phase 1):</strong> Professional installation of Python, virtual environment, and PyTorch (CUDA-enabled) on the Windows Server.</li>
                            <li><strong>AI Model Integration:</strong>
                                <ul class="list-circle list-inside ml-4">
                                    <li><strong>Phase 1:</strong> Installation, configuration, and initial functional testing of core speech models (Coqui TTS/XTTS v2, Whisper). All speech models must operate offline.</li>
                                    <li><strong>Phase 2:</strong> Full integration and testing of additional models (Bark, Meta AudioCraft Suite, and Wav2Lip &ndash; as an optional nice-to-have). For MusicGen and AudioGen, optional online operation should be explored and implemented if it significantly improves functionality. This includes the correct download and placement of model weights.</li>
                                </ul>
                            </li>
                            <li><strong>GUI Setup:</strong>
                                <ul class="list-circle list-inside ml-4">
                                    <li><strong>Phase 1:</strong> Launching and testing the standard Gradio demos of the integrated core models. <span class="text-secondary text-sm"><em>(This task focuses on verifying basic Gradio functionality. For the comprehensive GUI in Phase 2, please refer to Section 3a for our recommended approach of a dedicated frontend framework.)</em></span></li>
                                    <li><strong>Phase 2:</strong> Implementation of the advanced, polished GUI as discussed in Section 3a, based on the recommended framework.</li>
                                </ul>
                            </li>
                            <li><strong>Workflow Consulting &amp; Implementation:</strong>
                                <ul class="list-circle list-inside ml-4">
                                    <li><strong>Phase 1:</strong> Consultation on creating an **intuitive and fast workflow** for core Voice Cloning, TTS, and STS using a simple Gradio interface (as defined in Section 5).</li>
                                    <li><strong>Phase 2:</strong> Further consultation and implementation of **professional, comprehensive, and seamless workflows** incorporating all tools and the advanced GUI, enabling a **smooth and efficient operating process**. <span class="text-secondary text-sm"><em>(The framework for these advanced workflows will align with the architectural decision in Section 3a.)</em></span></li>
                                </ul>
                            </li>
                            <li><strong>Documentation &amp; Handover (Ongoing):</strong> Precise documentation of the steps performed and instructions for basic usage and troubleshooting. This will be ongoing throughout both phases, with a comprehensive handover at the end of each.</li>
                            <li><strong>Communication (Ongoing):</strong> Clear and proactive communication throughout the project.</li>
                        </ul>
                    </section>

                    <!-- Section: 5. Minimal System for Programmer Testing & Focus on Intuitive Workflow -->
                    <section id="minimal-system">
                        <h2 class="section-header">5. Phase 1: Minimal System (Core System for Test Gig) &amp; Focus on Intuitive Workflow</h2>
                        <p class="mb-4 text-primary">
                            A **minimal system as a starting point for the programmer is highly sensible and recommended.** It serves as an effective "proof of concept" and a test of the freelancer's skills and communication style. It reduces initial risk and ensures the programmer understands the technical requirements and, most importantly, our vision for an **intuitive workflow**.
                        </p>
                        <h3 class="text-xl font-bold mt-6 mb-3 text-accent">Proposed Minimal System for the Test Gig:</h3>
                        <p class="mb-4 text-primary">
                            The programmer should install the following core components and provide a simple, **intuitive Gradio GUI** that links them:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li>Installation &amp; configuration of Python, PyTorch (CUDA-enabled), Git, and FFmpeg on the Windows Server.</li>
                            <li>Installation &amp; initial functional testing of Coqui TTS (XTTS v2):
                                <ul class="list-circle list-inside ml-4">
                                    <li>Provision of a Gradio UI for XTTS v2.</li>
                                    <li>Functionality: Text-to-Speech (German &amp; English) with the ability to upload a reference voice to clone and apply its style/emotion.</li>
                                </ul>
                            </li>
                            <li>Installation &amp; initial functional testing of Whisper (large-v3):
                                <ul class="list-circle list-inside ml-4">
                                    <li>Functionality: Speech-to-Text to transcribe any audio file (German/English).</li>
                                </ul>
                            </li>
                            <li><strong>Linking of tools in a simple workflow (via Gradio):</strong>
                                <ul class="list-circle list-inside ml-4">
                                    <li>A **single, intuitive Gradio tab or section** that allows:
                                        <ul class="list-square list-inside ml-4">
                                            <li>Uploading an audio file.</li>
                                            <li>Transcribing this audio file using Whisper (displaying the text output).</li>
                                            <li>Inputting the transcribed text (or an adjusted text) into Coqui TTS (XTTS v2).</li>
                                            <li>Option to select a cloned reference voice (from the initial XTTS setup).</li>
                                            <li>Generating and playing the new speech output.</li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                        <p class="mt-4 text-primary">
                            This workflow should demonstrate how the tools can be seamlessly used for an STS (Speech-to-Text-to-Speech) process, with a **strong focus on the simplicity and speed of operation for a non-technical user.** <span class="text-secondary text-sm"><em>(Note: The framework for the comprehensive, production-ready GUI in Phase 2 will be determined based on the discussion in Section 3a, which recommends a dedicated frontend framework for optimal results.)</em></span>
                        </p>
                    </section>

                    <!-- Section: 6. Freelancer Profile Requirements -->
                    <section id="profile-requirements">
                        <h2 class="section-header">6. Freelancer Profile Requirements</h2>
                        <p class="mb-4 text-primary">
                            We are looking for a freelancer with the following qualifications and characteristics:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Proven Experience:</strong> Solid experience in Python programming, particularly in Machine Learning / Deep Learning.</li>
                            <li><strong>Specialization:</strong> Experience with or deep understanding of Speech AI models (TTS, Voice Cloning, STT, Lip Sync) and generative audio models (Text-to-Audio, Text-to-Music, Text-to-SFX).</li>
                            <li><strong>GPU Knowledge:</strong> Practical experience in setting up AI environments on Windows systems with NVIDIA GPUs (CUDA, PyTorch optimization).</li>
                            <li><strong>Open-Source Experience:</strong> Familiarity with working on open-source projects and their documentation (GitHub).</li>
                            <li><strong>Web UI Skills:</strong> Experience with Gradio (or Streamlit) is a significant plus, with a strong understanding of creating **user-friendly, intuitive, and fast workflows**. Given the discussion in Section 3a, experience with dedicated frontend frameworks (e.g., React.js) and their integration with local backend servers for advanced GUI implementation is also highly relevant and recommended for achieving the desired professional aesthetic.</li>
                            <li><strong>Problem-Solving:</strong> Ability to independently troubleshoot complex installation issues.</li>
                            <li><strong>Communication:</strong> Fluent in German or English.</li>
                            <li><strong>Reliability &amp; Professionalism:</strong> High ratings and positive references on the freelancer platform.</li>
                            <li><strong>Remote Access:</strong> Willingness and experience in securely accessing Windows servers remotely (e.g., via Remote Desktop, TeamViewer etc.). Security aspects must be discussed beforehand.</li>
                        </ul>
                    </section>

                    <!-- Section: 7. Project Framework & Offer -->
                    <section id="project-framework">
                        <h2 class="section-header">7. Project Framework &amp; Offer</h2>
                        <p class="mb-4 text-primary">
                            The project is divided into two phases, with the Minimal System (Phase 1) serving as a crucial initial assessment of the freelancer's capabilities and alignment with our workflow vision.
                        </p>
                        <h3 class="text-xl font-bold mt-6 mb-3 text-accent">Timeline &amp; Budget Request:</h3>
                        <p class="mb-4 text-primary">
                            Please provide **separate, realistic estimates** for both timeline and budget for the following phases:
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Phase 1: Minimal System (Core System Test Gig)</strong> &ndash; As defined in Section 5.</li>
                            <li><strong>Phase 2: Full System Implementation &amp; Advanced GUI</strong> &ndash; This phase includes:
                                <ul class="list-circle list-inside ml-4">
                                    <li>Full integration of all remaining AI models (Bark, Meta AudioCraft Suite, and Wav2Lip &ndash; as an optional nice-to-have).</li>
                                    <li>Implementation of the complete, polished GUI as discussed in Section 3a, utilizing a dedicated frontend framework if mutually recommended.</li>
                                    <li>Development of **professional, comprehensive, and seamless workflows** incorporating all tools.</li>
                                    <li>Integration of **multi-user functionality** where applicable (e.g., personalized GUI settings, collaborative project spaces).</li>
                                </ul>
                            </li>
                        </ul>
                        <p class="mb-4 text-primary">
                            Both offers should be **well-documented and traceable** regarding the services included and the estimated working hours.
                        </p>
                        <p class="mb-4 text-primary">
                            <strong>Application:</strong> In your application, please describe your relevant experience and how you would approach our project. Highlight which of the mentioned tools you have successfully used and how you would ensure secure remote access. **Please specifically address your approach to creating an intuitive, fast, and user-friendly Gradio workflow for the Minimal System (Phase 1), and your recommendations for the advanced GUI (Phase 2) based on the discussion in Section 3a.**
                        </p>
                        <p class="mb-4 text-primary">
                            We look forward to receiving your qualified proposals!
                        </p>
                    </section>

                    <!-- NEW Section: 8. Collaboration & Project Management -->
                    <section id="collaboration">
                        <h2 class="section-header">8. Collaboration &amp; Project Management</h2>
                        <p class="mb-4 text-primary">
                            Effective collaboration and transparent project management are paramount for the success of this project. We particularly value and look forward to your input on the look and feel, and the intuitive workflow of the system, happy to receive your suggestions.
                        </p>
                        <ul class="list-disc list-inside space-y-1 text-primary">
                            <li><strong>Communication Channels:</strong> We propose regular video meetings for alignment and in-depth discussions. For daily communication and quick queries, platforms like Slack or Microsoft Teams could be utilized.</li>
                            <li><strong>Code &amp; Version Control:</strong> All code will be managed using Git, with GitHub or GitLab as the central repository. A clear branching strategy (e.g., GitFlow or GitHub Flow) should be established collaboratively.</li>
                            <li><strong>Issue Tracking &amp; Task Management:</strong> We prefer using a structured issue tracking system (e.g., GitHub Issues, Jira, Trello) to manage tasks, bugs, and feature requests.</li>
                            <li><strong>Iterative GUI Development:</strong> The development of the GUI, especially the advanced features in Phase 2, is inherently an iterative process. We anticipate working closely with the programmer through multiple cycles of development, feedback, and revision to achieve the desired polish and usability. Your ability to translate design concepts into functional UI with a keen eye for user experience will be key.</li>
                        </ul>
                    </section>

                </div>
            </main>
        </div>
    </div>

    <!-- Modals (Generic dialogs for simulated actions) -->
    <div id="customModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header"></div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="hideModal()">OK</button>
            </div>
        </div>
    </div>


    <script>
        // No Chart.js instance needed for this version as there's no dynamic chart.

        document.addEventListener('DOMContentLoaded', function() {
            // Project Dropdown Logic in the Top Bar
            const projectDropdownBtn = document.getElementById('projectDropdownBtn');
            const projectDropdownContent = document.getElementById('projectDropdownContent');
            projectDropdownBtn.addEventListener('click', function() {
                projectDropdownContent.classList.toggle('hidden');
            });
            // Hide dropdown when clicking outside
            window.addEventListener('click', function(event) {
                if (!projectDropdownBtn.contains(event.target) && !projectDropdownContent.contains(event.target)) {
                    projectDropdownContent.classList.add('hidden');
                }
            });

            // Theme Toggle Logic
            const themeToggleBtn = document.getElementById('themeToggle');
            const body = document.body;

            // Helper function to get RGB values from hex
            function hexToRgb(hex) {
                const bigint = parseInt(hex.slice(1), 16);
                const r = (bigint >> 16) & 255;
                const g = (bigint >> 8) & 255;
                const b = bigint & 255;
                // Switched G and B to match desired blue tone more accurately in a previous iteration.
                // Keeping this as is, but typically it would be (r, g, b) directly.
                return `${r}, ${b}, ${g}`; 
            }

            // Function to set theme
            function setTheme(isDarkMode) {
                if (isDarkMode) {
                    body.classList.remove('light-mode');
                    localStorage.setItem('theme', 'dark');
                } else {
                    body.classList.add('light-mode');
                    localStorage.setItem('theme', 'light');
                }
                updateThemeIcon(isDarkMode);
                updateCssVariableRgbValues(); // Update RGB for backdrop-blur
            }

            // Function to update theme icon
            function updateThemeIcon(isDarkMode) {
                const iconSpan = themeToggleBtn.querySelector('span');
                iconSpan.textContent = isDarkMode ? '💡' : '🌙'; // Lightbulb for dark mode, Moon for light mode
            }

            // Function to update RGB CSS variables for backdrop-blur
            function updateCssVariableRgbValues() {
                const root = document.documentElement;
                const computedStyle = getComputedStyle(root);

                const primaryBgHex = computedStyle.getPropertyValue('--color-bg-primary').trim();
                const secondaryBgHex = computedStyle.getPropertyValue('--color-bg-secondary').trim();
                let accentBlueDarkHex = computedStyle.getPropertyValue('--color-accent-blue-dark').trim();

                if (!accentBlueDarkHex || !accentBlueDarkHex.startsWith('#') || accentBlueDarkHex.length !== 7) {
                    accentBlueDarkHex = body.classList.contains('light-mode') ? '#EAF2F8' : '#2C5282';
                }

                root.style.setProperty('--color-bg-primary-rgb', hexToRgb(primaryBgHex));
                root.style.setProperty('--color-bg-secondary-rgb', hexToRgb(secondaryBgHex));
                root.style.setProperty('--color-accent-blue-rgb', hexToRgb(accentBlueDarkHex));
            }

            // Check saved theme preference on load
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                setTheme(false); // Apply light mode
            } else {
                setTheme(true); // Default to dark mode if no preference or 'dark'
            }

            // Event listener for toggle button
            themeToggleBtn.addEventListener('click', () => {
                setTheme(!body.classList.contains('light-mode')); // Toggle theme
            });

            // Function to scroll to sections (used by sidebar nav links)
            window.scrollToSection = function(sectionId) {
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            };

            // Custom Modal Functions (generic dialogs for simulated actions from sidebar)
            const customModal = document.getElementById('customModal');
            const modalHeader = customModal.querySelector('.modal-header');
            const modalBody = customModal.querySelector('.modal-body');

            window.showCustomModal = function(headerText, bodyText) {
                modalHeader.textContent = headerText;
                modalBody.textContent = bodyText;
                customModal.style.display = 'flex'; // Show modal
            };

            window.hideModal = function() {
                customModal.style.display = 'none'; // Hide modal
            };

            updateCssVariableRgbValues(); // Set initial RGB variables
        });
    </script>
</body>
</html>

