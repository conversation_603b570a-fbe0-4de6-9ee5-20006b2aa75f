"""
Setup script for AI Audio System Phase 1
This script helps set up the environment and dependencies
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def check_cuda():
    """Check CUDA availability"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA is available: {torch.cuda.get_device_name()}")
            print(f"   CUDA Version: {torch.version.cuda}")
            return True
        else:
            print("⚠️ CUDA is not available, will use CPU")
            return False
    except ImportError:
        print("⚠️ PyTorch not installed yet, CUDA check will be done after installation")
        return False

def install_ffmpeg():
    """Install FFmpeg based on the operating system"""
    system = platform.system().lower()
    
    if system == "windows":
        print("📋 For Windows, please install FFmpeg manually:")
        print("   1. Download from: https://ffmpeg.org/download.html")
        print("   2. Extract and add to PATH")
        print("   3. Or use: winget install ffmpeg")
        return True
    elif system == "darwin":  # macOS
        return run_command("brew install ffmpeg", "Installing FFmpeg via Homebrew")
    elif system == "linux":
        return run_command("sudo apt-get update && sudo apt-get install -y ffmpeg", "Installing FFmpeg via apt")
    else:
        print(f"⚠️ Unknown system: {system}. Please install FFmpeg manually")
        return False

def setup_environment():
    """Set up the Python environment"""
    print("🚀 Setting up AI Audio System Phase 1 Environment")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install FFmpeg
    print("\n📦 Installing FFmpeg...")
    install_ffmpeg()
    
    # Install Python dependencies
    if not run_command("pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install PyTorch with CUDA support if available
    system = platform.system().lower()
    if system == "windows" or system == "linux":
        torch_command = "pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118"
    else:  # macOS
        torch_command = "pip install torch torchaudio"
    
    if not run_command(torch_command, "Installing PyTorch"):
        return False
    
    # Check CUDA after PyTorch installation
    check_cuda()
    
    # Install other requirements
    if not run_command("pip install -r requirements.txt", "Installing other dependencies"):
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Run: streamlit run streamlit_app.py")
    print("   2. Open your browser to the displayed URL")
    print("   3. Load the models using the sidebar")
    print("   4. Upload audio files and start testing!")
    
    return True

def main():
    """Main setup function"""
    try:
        setup_environment()
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")

if __name__ == "__main__":
    main()
