import streamlit as st
import torch
import os
import tempfile
import io
from TTS.api import TTS
import whisper
import soundfile as sf
import numpy as np
from pathlib import Path

# Set page config
st.set_page_config(
    page_title="AI Audio System - Phase 1",
    page_icon="🎙️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'tts_model' not in st.session_state:
    st.session_state.tts_model = None
if 'whisper_model' not in st.session_state:
    st.session_state.whisper_model = None
if 'generated_audio' not in st.session_state:
    st.session_state.generated_audio = None

@st.cache_resource
def load_tts_model():
    """Load TTS model with caching"""
    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        os.environ["TORCH_FORCE_NO_WEIGHTS_ONLY_LOAD"] = "1"
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
        return tts
    except Exception as e:
        st.error(f"Error loading TTS model: {str(e)}")
        return None

@st.cache_resource
def load_whisper_model():
    """Load Whisper model with caching"""
    try:
        model = whisper.load_model("large-v3")
        return model
    except Exception as e:
        st.error(f"Error loading Whisper model: {str(e)}")
        return None

def transcribe_audio(audio_file, model):
    """Transcribe audio using Whisper"""
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            tmp_file.write(audio_file.read())
            tmp_file_path = tmp_file.name
        
        result = model.transcribe(tmp_file_path)
        os.unlink(tmp_file_path)
        return result["text"]
    except Exception as e:
        st.error(f"Error transcribing audio: {str(e)}")
        return None

def generate_speech(text, reference_audio, tts_model, language="en"):
    """Generate speech using TTS"""
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as ref_file:
            ref_file.write(reference_audio.read())
            ref_file_path = ref_file.name
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as output_file:
            output_path = output_file.name
        
        tts_model.tts_to_file(
            text=text,
            speaker_wav=ref_file_path,
            language=language,
            file_path=output_path
        )
        
        # Read the generated audio
        with open(output_path, 'rb') as f:
            audio_data = f.read()
        
        # Cleanup
        os.unlink(ref_file_path)
        os.unlink(output_path)
        
        return audio_data
    except Exception as e:
        st.error(f"Error generating speech: {str(e)}")
        return None

# Main UI
st.title("🎙️ AI Audio System - Phase 1 Demo")
st.markdown("### Speech-to-Speech Conversion with Voice Cloning")

# Sidebar for model status
with st.sidebar:
    st.header("🔧 System Status")
    
    # Check CUDA availability
    if torch.cuda.is_available():
        st.success(f"✅ CUDA Available: {torch.cuda.get_device_name()}")
    else:
        st.warning("⚠️ CUDA not available, using CPU")
    
    # Model loading status
    st.subheader("Model Loading")
    
    if st.button("🔄 Load Models"):
        with st.spinner("Loading TTS model..."):
            st.session_state.tts_model = load_tts_model()
        with st.spinner("Loading Whisper model..."):
            st.session_state.whisper_model = load_whisper_model()
    
    if st.session_state.tts_model:
        st.success("✅ TTS Model Loaded")
    else:
        st.error("❌ TTS Model Not Loaded")
    
    if st.session_state.whisper_model:
        st.success("✅ Whisper Model Loaded")
    else:
        st.error("❌ Whisper Model Not Loaded")

# Main workflow
col1, col2 = st.columns([1, 1])

with col1:
    st.header("📤 Input Audio")
    
    # Audio upload
    uploaded_audio = st.file_uploader(
        "Upload audio file for transcription",
        type=['wav', 'mp3', 'flac', 'm4a'],
        help="Upload an audio file to transcribe and convert"
    )
    
    if uploaded_audio:
        st.audio(uploaded_audio, format='audio/wav')
        
        # Transcription
        if st.button("🎯 Transcribe Audio") and st.session_state.whisper_model:
            with st.spinner("Transcribing audio..."):
                transcribed_text = transcribe_audio(uploaded_audio, st.session_state.whisper_model)
                if transcribed_text:
                    st.session_state.transcribed_text = transcribed_text
                    st.success("Transcription completed!")

with col2:
    st.header("🎙️ Reference Voice")
    
    # Reference audio upload
    reference_audio = st.file_uploader(
        "Upload reference voice for cloning",
        type=['wav', 'mp3', 'flac'],
        help="Upload a clean audio sample of the target voice (3-10 seconds recommended)"
    )
    
    if reference_audio:
        st.audio(reference_audio, format='audio/wav')

# Text editing section
st.header("📝 Text Processing")

if 'transcribed_text' in st.session_state:
    edited_text = st.text_area(
        "Edit transcribed text:",
        value=st.session_state.transcribed_text,
        height=150,
        help="You can edit the transcribed text before generating speech"
    )
else:
    edited_text = st.text_area(
        "Or enter text directly:",
        height=150,
        placeholder="Enter the text you want to convert to speech..."
    )

# Language selection
language = st.selectbox(
    "Select language:",
    options=["en", "de", "es", "fr", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh-cn", "ja", "hu", "ko"],
    index=0,
    help="Select the language for speech generation"
)

# Generation section
st.header("🎵 Speech Generation")

if st.button("🚀 Generate Speech", type="primary"):
    if not edited_text.strip():
        st.error("Please provide text to convert to speech")
    elif not reference_audio:
        st.error("Please upload a reference audio file")
    elif not st.session_state.tts_model:
        st.error("Please load the TTS model first")
    else:
        with st.spinner("Generating speech... This may take a few moments."):
            generated_audio = generate_speech(
                edited_text, 
                reference_audio, 
                st.session_state.tts_model, 
                language
            )
            
            if generated_audio:
                st.session_state.generated_audio = generated_audio
                st.success("Speech generated successfully!")

# Output section
if st.session_state.generated_audio:
    st.header("🎧 Generated Audio")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.audio(st.session_state.generated_audio, format='audio/wav')
    
    with col2:
        st.download_button(
            label="💾 Download Audio",
            data=st.session_state.generated_audio,
            file_name="generated_speech.wav",
            mime="audio/wav"
        )

# Footer
st.markdown("---")
st.markdown(
    """
    **Phase 1 Demo Features:**
    - 🎤 Audio upload and playback
    - 🎯 Speech-to-text transcription (Whisper)
    - ✏️ Text editing capabilities
    - 🎙️ Voice cloning with reference audio
    - 🗣️ Text-to-speech generation (XTTS v2)
    - 🌍 Multi-language support
    - 💾 Audio download functionality
    """
)
